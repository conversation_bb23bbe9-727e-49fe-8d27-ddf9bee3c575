<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="net.fuzui.StudentInfo.mapper.GradeMapper">

    <!-- 为了返回List，类型而定义的resultMap -->
    <resultMap type="Grade" id="resultListGrade">
        <result column="cid" property="cid" />
        <result column="sid" property="tname" />
        <result column="grade" property="grade" />
        <result column="credits" property="credits" />
    </resultMap>
    
    <!-- 为了返回List，类型而定义的resultMap -->
    <resultMap type="Grade" id="endCourseResult">
        <result column="cid" property="cid" />
        <result column="cname" property="cname" />
        <result column="sid" property="tname" />
        <result column="grade" property="grade" />
        <result column="credits" property="credits" />
    </resultMap>

    <!-- 添加成绩 -->
    <insert id="insertGrade" parameterType="Grade">
		insert into grade(cid,sid,grade,credits)
		value (#{cid}, #{sid},#{grade},#{credits})
	</insert>

    <delete id="deleteGrade" parameterType="map">
        delete from grade where cid=#{cid} and sid=#{sid}
    </delete>
	
	<!-- 查询学生本人总学分 -->
	<select id="queryCreditsSum" resultType="java.lang.String">
		select sum(credits) from grade where sid = #{sid}
	</select>

    <!-- 根据学号查询本人已修课程 -->
    <select id="getEedCourseBySid" parameterType="map" resultMap="endCourseResult">
        SELECT g.cid,c.Cname,g.grade,g.credits from grade g INNER JOIN course c on c.cid = g.Cid WHERE g.sid = #{sid}
        <!-- limit #{pageNo} , #{pageSize} -->
    </select>
    <select id="getGrade" resultType="java.lang.Integer">
        select ifnull(sum(grade),-1) from grade where sid=#{sid} and cid=#{cid} limit 0,1
    </select>


</mapper>