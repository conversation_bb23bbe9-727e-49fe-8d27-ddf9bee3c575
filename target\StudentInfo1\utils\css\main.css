@charset "UTF-8";


/* index begin */


.index{
	
	background: #50a3a2;
	background: -webkit-linear-gradient(top left, #96fbc4 0%, #f9f586 100%);
	background: linear-gradient(to bottom right, #96fbc4 0%, #f9f586 100%);
	opacity: 0.8;
	position: absolute;
	top: 50%;
	left: 0;
	width: 100%;
	height: 500px;
	margin-top: -200px;
	overflow: hidden;
	font-family: '华文行楷', sans-serif;
	
}


.index_admin input{
	position: absolute;
	top: 150px;
	right:300px;
	background: #4dffff;
	opacity: 0.7;
	width: 100px;
	height:100px;
	border-radius: 50px;
	box-shadow:-10px 0 10px #646f69;
	font-family: '华文行楷', sans-serif;
	
}

.index_teacher input{
	position: absolute;
	top: 150px;
	left:300px;
	background: #4dffff;
	opacity: 0.7;
	width: 100px;
	height:100px;
	border-radius: 50px;
	box-shadow:-10px 0 10px #646f69;
}

.index_student input{
	position: absolute;
	top: 200px;
	right:47%;
	background: #4dffff;
	opacity: 0.7;
	width: 100px;
	height:100px;
	border-radius: 50px;
	box-shadow:-10px 0 10px #646f69;
}

.index_admin input:hover{
	
	opacity: 1.0;
	
	
}

.index_teacher input:hover{
	
	opacity: 1.0;
	
}

.index_student input:hover{
	
	opacity: 1.0;
	
}


/* index end */



/*login begin*/



.login{
	background: #50a3a2;
	background: -webkit-linear-gradient(top left, #96fbc4 0%, #f9f586 100%);
	background: linear-gradient(to bottom right, #96fbc4 0%, #f9f586 100%);
	opacity: 0.8;
	position: absolute;
	top: 40%;
	left: 0;
	width: 100%;
	height: 500px;
	margin-top: -200px;
	overflow: hidden;
	font-family: '华文行楷', sans-serif;
}


.login1{
	text-align:center;
	position: absolute;
	top:150px;
	right:600px;
	width:300px;
	height:200px;
font-family: '华文行楷', sans-serif;
	
}


.login_sr{
	
}

.login_sr input {
	 -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  outline: 0;
  background-color: white;
  border: 0;
  padding: 10px 15px;
  color: #209b6d;
  border-radius: 3px;
  width: 250px;
  cursor: pointer;
  font-size: 18px;
  -webkit-transition-duration: 0.25s;
  -moz-transition-duration: 0.25s;
  -ms-transition-duration: 0.25s;
          transition-duration: 0.25s;
  font-family: '华文行楷', sans-serif;
}

.login_sr input:hover {
  background-color: rgba(255, 255, 255, 0.4);
}
.login_sr input:focus {
  background-color: white;
  width: 300px;
  color: #53e3a6;
}

.login_submit{
	
}
.login_submit input{
	-webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  outline: 0;
  background-color: white;
  border: 0;
  padding: 10px 15px;
  color: #53e3a6;
  border-radius: 3px;
  width: 250px;
  cursor: pointer;
  font-size: 18px;
  -webkit-transition-duration: 0.4s;
          transition-duration: 0.4s;
  font-family: '华文行楷', sans-serif;
  border-radius:10px;
}


.resh{
	
}

.resh input{
	-webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  outline: 0;
  background-color: white;
  border: 0;
  padding: 10px 15px;
  color: #53e3a6;
  border-radius: 3px;
  width: 100px;
  cursor: pointer;
  font-size: 18px;
  -webkit-transition-duration: 0.4s;
          transition-duration: 0.4s;
  font-family: '华文行楷', sans-serif;
  border-radius:10px;
}
.resh input:hover{
	opacity: 0.7;
}


/*login end*/




/*left begin*/



.demo {
	margin: 40px auto 0;
	width: 170px;
	text-align: center;
}
.menu {
	position: relative;
	width: 170px;
	padding: 5px 0;
	line-height: 35px;
	border-radius: 5px;
	background: -*-linear-gradient(top,#dbdbdb,#999);
}
.menu a {
	display: block;
	color: #484848;
	text-decoration: none;
	text-shadow: 0 1px 0 #e0e0e0;
	font-size: 14px;
}

.menu li:hover a{
	color: #fff;
	 background:rgba(0,0,0,0.6);
	
	-moz-box-shadow: 10px 10px 5px #888888; /* 老的 Firefox */
	box-shadow: 10px 10px 5px #888888;
	-webkit-transition-duration: 0.75s;
          transition-duration: 0.75s;
}
.menu li {
	position: relative;
	z-index: 2;
}
















/*left end*/

/*main begin*/
html, body {
	margin: 0;
	padding: 0;
	height: 100%;
	width: 100%;
	border: none;
}

.main {
	width: 100%;
	height: 100%;
	text-align: left;
	background:url(../image/main.png);
	opacity: 0.9;
	
	
}
.main1{
	height:500px;
	overflow-y:scroll;
	text-align:center;
}

.main2{
	
}


table{
	margin:0 auto;
}



table thead, table tr {
border-top-width: 1px;
border-top-style: solid;
border-top-color: rgb(230, 189, 189);
}
table {
border-bottom-width: 1px;
border-bottom-style: solid;
border-bottom-color: rgb(230, 189, 189);
}


table td, table th {
padding: 5px 10px;
font-size: 12px;
font-family: Verdana;
color: rgb(177, 106, 104);
}


table tr:nth-child(even) {
background: rgb(238, 211, 210)
}
table tr:nth-child(odd) {
background: #FFF
}




.main_left {
	width: 250px;
	
	background:url(../image/left.png);
	opacity: 0.9;
}



.picBox {
	width: 20px;
	background: #337ABB url(../image/left1.png) no-repeat center center;
	opacity: 0.5;
}

.main_left, .picBox {
	float: left;
	height: 100%;
	_margin-right: -3px;
}

/*main end*/