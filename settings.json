{"window.commandCenter": 1, "remote.SSH.remotePlatform": {"hzh.sealos.run_ns-xqwvo54j_dvuepro": "linux"}, "files.autoSave": "after<PERSON>elay", "python.createEnvironment.trigger": "off", "workbench.editorAssociations": {"*.doc": "default"}, "editor.unicodeHighlight.invisibleCharacters": false, "files.autoGuessEncoding": true, "liveServer.settings.donotShowInfoMsg": true, "workbench.activityBar.orientation": "vertical", "cursor.general.disableHttp2": true, "augment.advanced": {}, "trust.mode": "off", "extensions.trusted": false, "security.workspace.trust.enabled": false, "extensions.allowed": {"abusaidm.html-snippets": true, "formulahendry.code-runner": true, "hopesy.cursor-fake-machine": true, "labring.devbox-aio": true, "mqycn.huile8": true, "ms-ceintl.vscode-language-pack-zh-hans": true, "ms-edgedevtools.vscode-edge-devtools": false, "ms-python.debugpy": true, "ms-python.python": true, "ms-python.vscode-pylance": true, "ms-vscode-remote.remote-ssh": true, "ms-vscode-remote.remote-ssh-edit": true, "ms-vscode.remote-explorer": true, "ritwickdey.liveserver": false, "starkwang.markdown": true, "techer.open-in-browser": false, "tencent-cloud.coding-copilot": true, "tomoki1207.pdf": true, "twxs.cmake": true}, "extensions.confirmedUriHandlerExtensionIds": [], "extensions.autoUpdate": "none", "extensions.autoCheckUpdates": false, "extensions.supportUntrustedWorkspaces": {}}