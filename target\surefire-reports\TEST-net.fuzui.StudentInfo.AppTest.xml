<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="net.fuzui.StudentInfo.AppTest" time="0.012" tests="1" errors="0" skipped="0" failures="0">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="C:\Users\<USER>\Desktop\StudentInfo1\target\test-classes;C:\Users\<USER>\Desktop\StudentInfo1\target\classes;C:\Users\<USER>\.m2\repository\junit\junit\4.12\junit-4.12.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\4.3.3.RELEASE\spring-test-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.3.1\commons-fileupload-1.3.1.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.2\commons-io-2.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.7\slf4j-api-1.7.7.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-access\1.1.2\logback-access-1.1.2.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.1.2\logback-core-1.1.2.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.1.2\logback-classic-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jackson\jackson-mapper-asl\1.9.13\jackson-mapper-asl-1.9.13.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jackson\jackson-core-asl\1.9.13\jackson-core-asl-1.9.13.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.1.0\jackson-core-2.1.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.1.0\jackson-databind-2.1.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.1.0\jackson-annotations-2.1.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\4.3.3.RELEASE\spring-aop-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\4.3.3.RELEASE\spring-beans-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\4.3.3.RELEASE\spring-context-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\4.3.3.RELEASE\spring-expression-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\4.3.3.RELEASE\spring-context-support-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\4.3.3.RELEASE\spring-core-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\4.3.3.RELEASE\spring-jdbc-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\4.3.3.RELEASE\spring-oxm-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\4.3.3.RELEASE\spring-orm-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\4.3.3.RELEASE\spring-tx-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\4.3.3.RELEASE\spring-webmvc-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\4.3.3.RELEASE\spring-web-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjrt\1.8.6\aspectjrt-1.8.6.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjlib\1.6.2\aspectjlib-1.6.2.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.8.6\aspectjweaver-1.8.6.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis\3.4.4\mybatis-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis-spring\1.3.1\mybatis-spring-1.3.1.jar;C:\Users\<USER>\.m2\repository\com\alibaba\druid\1.0.15\druid-1.0.15.jar;D:\JDK_8\jre\..\lib\jconsole.jar;D:\JDK_8\jre\..\lib\tools.jar;C:\Users\<USER>\.m2\repository\com\mysql\mysql-connector-j\8.0.32\mysql-connector-j-8.0.32.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\3.21.9\protobuf-java-3.21.9.jar;C:\Users\<USER>\.m2\repository\javax\servlet\jstl\1.2\jstl-1.2.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.0.4\commons-logging-1.0.4.jar;C:\Users\<USER>\.m2\repository\commons-lang\commons-lang\2.3\commons-lang-2.3.jar;C:\Users\<USER>\.m2\repository\commons-collections\commons-collections\3.2\commons-collections-3.2.jar;C:\Users\<USER>\.m2\repository\commons-beanutils\commons-beanutils\1.7.0\commons-beanutils-1.7.0.jar;C:\Users\<USER>\.m2\repository\com\hynnet\json-lib\2.4\json-lib-2.4.jar;C:\Users\<USER>\.m2\repository\com\alibaba\fastjson\1.2.54\fastjson-1.2.54.jar;C:\Users\<USER>\.m2\repository\net\sf\ezmorph\ezmorph\1.0.4\ezmorph-1.0.4.jar;C:\Users\<USER>\.m2\repository\javax\servlet\javax.servlet-api\4.0.1\javax.servlet-api-4.0.1.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper\5.0.0\pagehelper-5.0.0.jar;C:\Users\<USER>\.m2\repository\com\github\jsqlparser\jsqlparser\0.9.5\jsqlparser-0.9.5.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Windows 11"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="D:\JDK_8\jre\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire3560249686576887197\surefirebooter3974296841271476944.jar C:\Users\<USER>\AppData\Local\Temp\surefire3560249686576887197 2025-08-10T22-11-00_943-jvmRun1 surefire711028817396800016tmp surefire_08277642877312824349tmp"/>
    <property name="surefire.test.class.path" value="C:\Users\<USER>\Desktop\StudentInfo1\target\test-classes;C:\Users\<USER>\Desktop\StudentInfo1\target\classes;C:\Users\<USER>\.m2\repository\junit\junit\4.12\junit-4.12.jar;C:\Users\<USER>\.m2\repository\org\hamcrest\hamcrest-core\1.3\hamcrest-core-1.3.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-test\4.3.3.RELEASE\spring-test-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\commons-fileupload\commons-fileupload\1.3.1\commons-fileupload-1.3.1.jar;C:\Users\<USER>\.m2\repository\commons-io\commons-io\2.2\commons-io-2.2.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\1.7.7\slf4j-api-1.7.7.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-access\1.1.2\logback-access-1.1.2.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-core\1.1.2\logback-core-1.1.2.jar;C:\Users\<USER>\.m2\repository\ch\qos\logback\logback-classic\1.1.2\logback-classic-1.1.2.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jackson\jackson-mapper-asl\1.9.13\jackson-mapper-asl-1.9.13.jar;C:\Users\<USER>\.m2\repository\org\codehaus\jackson\jackson-core-asl\1.9.13\jackson-core-asl-1.9.13.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-core\2.1.0\jackson-core-2.1.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-databind\2.1.0\jackson-databind-2.1.0.jar;C:\Users\<USER>\.m2\repository\com\fasterxml\jackson\core\jackson-annotations\2.1.0\jackson-annotations-2.1.0.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-aop\4.3.3.RELEASE\spring-aop-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-beans\4.3.3.RELEASE\spring-beans-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context\4.3.3.RELEASE\spring-context-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-expression\4.3.3.RELEASE\spring-expression-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-context-support\4.3.3.RELEASE\spring-context-support-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-core\4.3.3.RELEASE\spring-core-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-jdbc\4.3.3.RELEASE\spring-jdbc-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-oxm\4.3.3.RELEASE\spring-oxm-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-orm\4.3.3.RELEASE\spring-orm-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-tx\4.3.3.RELEASE\spring-tx-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-webmvc\4.3.3.RELEASE\spring-webmvc-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\springframework\spring-web\4.3.3.RELEASE\spring-web-4.3.3.RELEASE.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjrt\1.8.6\aspectjrt-1.8.6.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjlib\1.6.2\aspectjlib-1.6.2.jar;C:\Users\<USER>\.m2\repository\org\aspectj\aspectjweaver\1.8.6\aspectjweaver-1.8.6.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis\3.4.4\mybatis-3.4.4.jar;C:\Users\<USER>\.m2\repository\org\mybatis\mybatis-spring\1.3.1\mybatis-spring-1.3.1.jar;C:\Users\<USER>\.m2\repository\com\alibaba\druid\1.0.15\druid-1.0.15.jar;D:\JDK_8\jre\..\lib\jconsole.jar;D:\JDK_8\jre\..\lib\tools.jar;C:\Users\<USER>\.m2\repository\com\mysql\mysql-connector-j\8.0.32\mysql-connector-j-8.0.32.jar;C:\Users\<USER>\.m2\repository\com\google\protobuf\protobuf-java\3.21.9\protobuf-java-3.21.9.jar;C:\Users\<USER>\.m2\repository\javax\servlet\jstl\1.2\jstl-1.2.jar;C:\Users\<USER>\.m2\repository\commons-logging\commons-logging\1.0.4\commons-logging-1.0.4.jar;C:\Users\<USER>\.m2\repository\commons-lang\commons-lang\2.3\commons-lang-2.3.jar;C:\Users\<USER>\.m2\repository\commons-collections\commons-collections\3.2\commons-collections-3.2.jar;C:\Users\<USER>\.m2\repository\commons-beanutils\commons-beanutils\1.7.0\commons-beanutils-1.7.0.jar;C:\Users\<USER>\.m2\repository\com\hynnet\json-lib\2.4\json-lib-2.4.jar;C:\Users\<USER>\.m2\repository\com\alibaba\fastjson\1.2.54\fastjson-1.2.54.jar;C:\Users\<USER>\.m2\repository\net\sf\ezmorph\ezmorph\1.0.4\ezmorph-1.0.4.jar;C:\Users\<USER>\.m2\repository\javax\servlet\javax.servlet-api\4.0.1\javax.servlet-api-4.0.1.jar;C:\Users\<USER>\.m2\repository\com\github\pagehelper\pagehelper\5.0.0\pagehelper-5.0.0.jar;C:\Users\<USER>\.m2\repository\com\github\jsqlparser\jsqlparser\0.9.5\jsqlparser-0.9.5.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\JDK_8\jre"/>
    <property name="basedir" value="C:\Users\<USER>\Desktop\StudentInfo1"/>
    <property name="file.separator" value="\"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire3560249686576887197\surefirebooter3974296841271476944.jar"/>
    <property name="sun.boot.class.path" value="D:\JDK_8\jre\lib\resources.jar;D:\JDK_8\jre\lib\rt.jar;D:\JDK_8\jre\lib\jsse.jar;D:\JDK_8\jre\lib\jce.jar;D:\JDK_8\jre\lib\charsets.jar;D:\JDK_8\jre\lib\jfr.jar;D:\JDK_8\jre\classes"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="1.8.0_361-b09"/>
    <property name="user.name" value="Lenovo"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.endorsed.dirs" value="D:\JDK_8\jre\lib\endorsed"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="idea.version" value="2025.1.1.1"/>
    <property name="java.version" value="1.8.0_361"/>
    <property name="user.dir" value="C:\Users\<USER>\Desktop\StudentInfo1"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="D:\JDK_8\jre\bin;C:\windows\Sun\Java\bin;C:\windows\system32;C:\windows;D:\VMware 17Pro安装包\bin\;D:\ShadowBot;C:\windows\system32;C:\windows;C:\windows\System32\Wbem;C:\windows\System32\WindowsPowerShell\v1.0\;C:\windows\System32\OpenSSH\;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;D:\rose\common;C:\Program Files\Microsoft SQL Server\150\Tools\Binn\;C:\Program Files\Microsoft SQL Server\Client SDK\ODBC\170\Tools\Binn\;C:\Program Files\dotnet\;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;D:\node\;D:\node;D:\node\node_global;D:\node\node_cache;D:\dev\Dev-Cpp\MinGW64\bin;C:\Users\<USER>\.npm-global;D:\jdk\bin;D:\Google\Google\Chrome\Application;D:\pythun3.2;D:\pythun3.2\Scripts;D:\android\sdk\platform-tools;D:\android\sdk\tools;D:\apache-tomcat-9.0.107\bin;D:\ShadowBot;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\.dotnet\tools;D:\IntelliJ IDEA 2024.2.3\bin;D:\node\node_global;D:\cursor\resources\app\bin;D:\Fiddler;D:\Microsoft VS Code\bin;."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="25.361-b09"/>
    <property name="java.specification.maintenance.version" value="4"/>
    <property name="java.ext.dirs" value="D:\JDK_8\jre\lib\ext;C:\windows\Sun\Java\lib\ext"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="52.0"/>
  </properties>
  <testcase name="testApp" classname="net.fuzui.StudentInfo.AppTest" time="0.002"/>
</testsuite>