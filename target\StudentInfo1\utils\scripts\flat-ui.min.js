!function(t,e){var n,i,s=0,r=/^ui-id-\d+$/;function o(e,n){var i,s,r,o=e.nodeName.toLowerCase();return"area"===o?(s=(i=e.parentNode).name,!(!e.href||!s||"map"!==i.nodeName.toLowerCase())&&(!!(r=t("img[usemap=#"+s+"]")[0])&&a(r))):(/input|select|textarea|button|object/.test(o)?!e.disabled:"a"===o&&e.href||n)&&a(e)}function a(e){return t.expr.filters.visible(e)&&!t(e).parents().addBack().filter(function(){return"hidden"===t.css(this,"visibility")}).length}t.ui=t.ui||{},t.extend(t.ui,{version:"1.10.4",keyCode:{BACKSPACE:8,COMMA:188,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,LEFT:37,NUMPAD_ADD:107,NUMPAD_DECIMAL:110,NUMPAD_DIVIDE:111,NUMPAD_ENTER:108,NUMPAD_MULTIPLY:106,NUMPAD_SUBTRACT:109,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SPACE:32,TAB:9,UP:38}}),t.fn.extend({focus:(n=t.fn.focus,function(e,i){return"number"==typeof e?this.each(function(){var n=this;setTimeout(function(){t(n).focus(),i&&i.call(n)},e)}):n.apply(this,arguments)}),scrollParent:function(){var e;return e=t.ui.ie&&/(static|relative)/.test(this.css("position"))||/absolute/.test(this.css("position"))?this.parents().filter(function(){return/(relative|absolute|fixed)/.test(t.css(this,"position"))&&/(auto|scroll)/.test(t.css(this,"overflow")+t.css(this,"overflow-y")+t.css(this,"overflow-x"))}).eq(0):this.parents().filter(function(){return/(auto|scroll)/.test(t.css(this,"overflow")+t.css(this,"overflow-y")+t.css(this,"overflow-x"))}).eq(0),/fixed/.test(this.css("position"))||!e.length?t(document):e},zIndex:function(e){if(void 0!==e)return this.css("zIndex",e);if(this.length)for(var n,i,s=t(this[0]);s.length&&s[0]!==document;){if(("absolute"===(n=s.css("position"))||"relative"===n||"fixed"===n)&&(i=parseInt(s.css("zIndex"),10),!isNaN(i)&&0!==i))return i;s=s.parent()}return 0},uniqueId:function(){return this.each(function(){this.id||(this.id="ui-id-"+ ++s)})},removeUniqueId:function(){return this.each(function(){r.test(this.id)&&t(this).removeAttr("id")})}}),t.extend(t.expr[":"],{data:t.expr.createPseudo?t.expr.createPseudo(function(e){return function(n){return!!t.data(n,e)}}):function(e,n,i){return!!t.data(e,i[3])},focusable:function(e){return o(e,!isNaN(t.attr(e,"tabindex")))},tabbable:function(e){var n=t.attr(e,"tabindex"),i=isNaN(n);return(i||n>=0)&&o(e,!i)}}),t("<a>").outerWidth(1).jquery||t.each(["Width","Height"],function(e,n){var i="Width"===n?["Left","Right"]:["Top","Bottom"],s=n.toLowerCase(),r={innerWidth:t.fn.innerWidth,innerHeight:t.fn.innerHeight,outerWidth:t.fn.outerWidth,outerHeight:t.fn.outerHeight};function o(e,n,s,r){return t.each(i,function(){n-=parseFloat(t.css(e,"padding"+this))||0,s&&(n-=parseFloat(t.css(e,"border"+this+"Width"))||0),r&&(n-=parseFloat(t.css(e,"margin"+this))||0)}),n}t.fn["inner"+n]=function(e){return void 0===e?r["inner"+n].call(this):this.each(function(){t(this).css(s,o(this,e)+"px")})},t.fn["outer"+n]=function(e,i){return"number"!=typeof e?r["outer"+n].call(this,e):this.each(function(){t(this).css(s,o(this,e,!0,i)+"px")})}}),t.fn.addBack||(t.fn.addBack=function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}),t("<a>").data("a-b","a").removeData("a-b").data("a-b")&&(t.fn.removeData=(i=t.fn.removeData,function(e){return arguments.length?i.call(this,t.camelCase(e)):i.call(this)})),t.ui.ie=!!/msie [\w.]+/.exec(navigator.userAgent.toLowerCase()),t.support.selectstart="onselectstart"in document.createElement("div"),t.fn.extend({disableSelection:function(){return this.bind((t.support.selectstart?"selectstart":"mousedown")+".ui-disableSelection",function(t){t.preventDefault()})},enableSelection:function(){return this.unbind(".ui-disableSelection")}}),t.extend(t.ui,{plugin:{add:function(e,n,i){var s,r=t.ui[e].prototype;for(s in i)r.plugins[s]=r.plugins[s]||[],r.plugins[s].push([n,i[s]])},call:function(t,e,n){var i,s=t.plugins[e];if(s&&t.element[0].parentNode&&11!==t.element[0].parentNode.nodeType)for(i=0;i<s.length;i++)t.options[s[i][0]]&&s[i][1].apply(t.element,n)}},hasScroll:function(e,n){if("hidden"===t(e).css("overflow"))return!1;var i,s=n&&"left"===n?"scrollLeft":"scrollTop";return e[s]>0||(e[s]=1,i=e[s]>0,e[s]=0,i)}})}(jQuery),function(t,e){var n=0,i=Array.prototype.slice,s=t.cleanData;t.cleanData=function(e){for(var n,i=0;null!=(n=e[i]);i++)try{t(n).triggerHandler("remove")}catch(t){}s(e)},t.widget=function(e,n,i){var s,r,o,a,l={},c=e.split(".")[0];e=e.split(".")[1],s=c+"-"+e,i||(i=n,n=t.Widget),t.expr[":"][s.toLowerCase()]=function(e){return!!t.data(e,s)},t[c]=t[c]||{},r=t[c][e],o=t[c][e]=function(t,e){if(!this._createWidget)return new o(t,e);arguments.length&&this._createWidget(t,e)},t.extend(o,r,{version:i.version,_proto:t.extend({},i),_childConstructors:[]}),(a=new n).options=t.widget.extend({},a.options),t.each(i,function(e,i){var s,r;t.isFunction(i)?l[e]=(s=function(){return n.prototype[e].apply(this,arguments)},r=function(t){return n.prototype[e].apply(this,t)},function(){var t,e=this._super,n=this._superApply;return this._super=s,this._superApply=r,t=i.apply(this,arguments),this._super=e,this._superApply=n,t}):l[e]=i}),o.prototype=t.widget.extend(a,{widgetEventPrefix:r&&a.widgetEventPrefix||e},l,{constructor:o,namespace:c,widgetName:e,widgetFullName:s}),r?(t.each(r._childConstructors,function(e,n){var i=n.prototype;t.widget(i.namespace+"."+i.widgetName,o,n._proto)}),delete r._childConstructors):n._childConstructors.push(o),t.widget.bridge(e,o)},t.widget.extend=function(e){for(var n,s,r=i.call(arguments,1),o=0,a=r.length;o<a;o++)for(n in r[o])s=r[o][n],r[o].hasOwnProperty(n)&&void 0!==s&&(t.isPlainObject(s)?e[n]=t.isPlainObject(e[n])?t.widget.extend({},e[n],s):t.widget.extend({},s):e[n]=s);return e},t.widget.bridge=function(e,n){var s=n.prototype.widgetFullName||e;t.fn[e]=function(r){var o="string"==typeof r,a=i.call(arguments,1),l=this;return r=!o&&a.length?t.widget.extend.apply(null,[r].concat(a)):r,o?this.each(function(){var n,i=t.data(this,s);return i?t.isFunction(i[r])&&"_"!==r.charAt(0)?(n=i[r].apply(i,a))!==i&&void 0!==n?(l=n&&n.jquery?l.pushStack(n.get()):n,!1):void 0:t.error("no such method '"+r+"' for "+e+" widget instance"):t.error("cannot call methods on "+e+" prior to initialization; attempted to call method '"+r+"'")}):this.each(function(){var e=t.data(this,s);e?e.option(r||{})._init():t.data(this,s,new n(r,this))}),l}},t.Widget=function(){},t.Widget._childConstructors=[],t.Widget.prototype={widgetName:"widget",widgetEventPrefix:"",defaultElement:"<div>",options:{disabled:!1,create:null},_createWidget:function(e,i){i=t(i||this.defaultElement||this)[0],this.element=t(i),this.uuid=n++,this.eventNamespace="."+this.widgetName+this.uuid,this.options=t.widget.extend({},this.options,this._getCreateOptions(),e),this.bindings=t(),this.hoverable=t(),this.focusable=t(),i!==this&&(t.data(i,this.widgetFullName,this),this._on(!0,this.element,{remove:function(t){t.target===i&&this.destroy()}}),this.document=t(i.style?i.ownerDocument:i.document||i),this.window=t(this.document[0].defaultView||this.document[0].parentWindow)),this._create(),this._trigger("create",null,this._getCreateEventData()),this._init()},_getCreateOptions:t.noop,_getCreateEventData:t.noop,_create:t.noop,_init:t.noop,destroy:function(){this._destroy(),this.element.unbind(this.eventNamespace).removeData(this.widgetName).removeData(this.widgetFullName).removeData(t.camelCase(this.widgetFullName)),this.widget().unbind(this.eventNamespace).removeAttr("aria-disabled").removeClass(this.widgetFullName+"-disabled ui-state-disabled"),this.bindings.unbind(this.eventNamespace),this.hoverable.removeClass("ui-state-hover"),this.focusable.removeClass("ui-state-focus")},_destroy:t.noop,widget:function(){return this.element},option:function(e,n){var i,s,r,o=e;if(0===arguments.length)return t.widget.extend({},this.options);if("string"==typeof e)if(o={},e=(i=e.split(".")).shift(),i.length){for(s=o[e]=t.widget.extend({},this.options[e]),r=0;r<i.length-1;r++)s[i[r]]=s[i[r]]||{},s=s[i[r]];if(e=i.pop(),1===arguments.length)return void 0===s[e]?null:s[e];s[e]=n}else{if(1===arguments.length)return void 0===this.options[e]?null:this.options[e];o[e]=n}return this._setOptions(o),this},_setOptions:function(t){var e;for(e in t)this._setOption(e,t[e]);return this},_setOption:function(t,e){return this.options[t]=e,"disabled"===t&&(this.widget().toggleClass(this.widgetFullName+"-disabled ui-state-disabled",!!e).attr("aria-disabled",e),this.hoverable.removeClass("ui-state-hover"),this.focusable.removeClass("ui-state-focus")),this},enable:function(){return this._setOption("disabled",!1)},disable:function(){return this._setOption("disabled",!0)},_on:function(e,n,i){var s,r=this;"boolean"!=typeof e&&(i=n,n=e,e=!1),i?(n=s=t(n),this.bindings=this.bindings.add(n)):(i=n,n=this.element,s=this.widget()),t.each(i,function(i,o){function a(){if(e||!0!==r.options.disabled&&!t(this).hasClass("ui-state-disabled"))return("string"==typeof o?r[o]:o).apply(r,arguments)}"string"!=typeof o&&(a.guid=o.guid=o.guid||a.guid||t.guid++);var l=i.match(/^(\w+)\s*(.*)$/),c=l[1]+r.eventNamespace,h=l[2];h?s.delegate(h,c,a):n.bind(c,a)})},_off:function(t,e){e=(e||"").split(" ").join(this.eventNamespace+" ")+this.eventNamespace,t.unbind(e).undelegate(e)},_delay:function(t,e){var n=this;return setTimeout(function(){return("string"==typeof t?n[t]:t).apply(n,arguments)},e||0)},_hoverable:function(e){this.hoverable=this.hoverable.add(e),this._on(e,{mouseenter:function(e){t(e.currentTarget).addClass("ui-state-hover")},mouseleave:function(e){t(e.currentTarget).removeClass("ui-state-hover")}})},_focusable:function(e){this.focusable=this.focusable.add(e),this._on(e,{focusin:function(e){t(e.currentTarget).addClass("ui-state-focus")},focusout:function(e){t(e.currentTarget).removeClass("ui-state-focus")}})},_trigger:function(e,n,i){var s,r,o=this.options[e];if(i=i||{},(n=t.Event(n)).type=(e===this.widgetEventPrefix?e:this.widgetEventPrefix+e).toLowerCase(),n.target=this.element[0],r=n.originalEvent)for(s in r)s in n||(n[s]=r[s]);return this.element.trigger(n,i),!(t.isFunction(o)&&!1===o.apply(this.element[0],[n].concat(i))||n.isDefaultPrevented())}},t.each({show:"fadeIn",hide:"fadeOut"},function(e,n){t.Widget.prototype["_"+e]=function(i,s,r){"string"==typeof s&&(s={effect:s});var o,a=s?!0===s||"number"==typeof s?n:s.effect||n:e;"number"==typeof(s=s||{})&&(s={duration:s}),o=!t.isEmptyObject(s),s.complete=r,s.delay&&i.delay(s.delay),o&&t.effects&&t.effects.effect[a]?i[e](s):a!==e&&i[a]?i[a](s.duration,s.easing,r):i.queue(function(n){t(this)[e](),r&&r.call(i[0]),n()})}})}(jQuery),function(t,e){var n=!1;t(document).mouseup(function(){n=!1}),t.widget("ui.mouse",{version:"1.10.4",options:{cancel:"input,textarea,button,select,option",distance:1,delay:0},_mouseInit:function(){var e=this;this.element.bind("mousedown."+this.widgetName,function(t){return e._mouseDown(t)}).bind("click."+this.widgetName,function(n){if(!0===t.data(n.target,e.widgetName+".preventClickEvent"))return t.removeData(n.target,e.widgetName+".preventClickEvent"),n.stopImmediatePropagation(),!1}),this.started=!1},_mouseDestroy:function(){this.element.unbind("."+this.widgetName),this._mouseMoveDelegate&&t(document).unbind("mousemove."+this.widgetName,this._mouseMoveDelegate).unbind("mouseup."+this.widgetName,this._mouseUpDelegate)},_mouseDown:function(e){if(!n){this._mouseStarted&&this._mouseUp(e),this._mouseDownEvent=e;var i=this,s=1===e.which,r=!("string"!=typeof this.options.cancel||!e.target.nodeName)&&t(e.target).closest(this.options.cancel).length;return!(s&&!r&&this._mouseCapture(e))||(this.mouseDelayMet=!this.options.delay,this.mouseDelayMet||(this._mouseDelayTimer=setTimeout(function(){i.mouseDelayMet=!0},this.options.delay)),this._mouseDistanceMet(e)&&this._mouseDelayMet(e)&&(this._mouseStarted=!1!==this._mouseStart(e),!this._mouseStarted)?(e.preventDefault(),!0):(!0===t.data(e.target,this.widgetName+".preventClickEvent")&&t.removeData(e.target,this.widgetName+".preventClickEvent"),this._mouseMoveDelegate=function(t){return i._mouseMove(t)},this._mouseUpDelegate=function(t){return i._mouseUp(t)},t(document).bind("mousemove."+this.widgetName,this._mouseMoveDelegate).bind("mouseup."+this.widgetName,this._mouseUpDelegate),e.preventDefault(),n=!0,!0))}},_mouseMove:function(e){return t.ui.ie&&(!document.documentMode||document.documentMode<9)&&!e.button?this._mouseUp(e):this._mouseStarted?(this._mouseDrag(e),e.preventDefault()):(this._mouseDistanceMet(e)&&this._mouseDelayMet(e)&&(this._mouseStarted=!1!==this._mouseStart(this._mouseDownEvent,e),this._mouseStarted?this._mouseDrag(e):this._mouseUp(e)),!this._mouseStarted)},_mouseUp:function(e){return t(document).unbind("mousemove."+this.widgetName,this._mouseMoveDelegate).unbind("mouseup."+this.widgetName,this._mouseUpDelegate),this._mouseStarted&&(this._mouseStarted=!1,e.target===this._mouseDownEvent.target&&t.data(e.target,this.widgetName+".preventClickEvent",!0),this._mouseStop(e)),!1},_mouseDistanceMet:function(t){return Math.max(Math.abs(this._mouseDownEvent.pageX-t.pageX),Math.abs(this._mouseDownEvent.pageY-t.pageY))>=this.options.distance},_mouseDelayMet:function(){return this.mouseDelayMet},_mouseStart:function(){},_mouseDrag:function(){},_mouseStop:function(){},_mouseCapture:function(){return!0}})}(jQuery),function(t,e){t.ui=t.ui||{};var n,i=Math.max,s=Math.abs,r=Math.round,o=/left|center|right/,a=/top|center|bottom/,l=/[\+\-]\d+(\.[\d]+)?%?/,c=/^\w+/,h=/%$/,u=t.fn.position;function d(t,e,n){return[parseFloat(t[0])*(h.test(t[0])?e/100:1),parseFloat(t[1])*(h.test(t[1])?n/100:1)]}function f(e,n){return parseInt(t.css(e,n),10)||0}t.position={scrollbarWidth:function(){if(void 0!==n)return n;var e,i,s=t("<div style='display:block;position:absolute;width:50px;height:50px;overflow:hidden;'><div style='height:100px;width:auto;'></div></div>"),r=s.children()[0];return t("body").append(s),e=r.offsetWidth,s.css("overflow","scroll"),e===(i=r.offsetWidth)&&(i=s[0].clientWidth),s.remove(),n=e-i},getScrollInfo:function(e){var n=e.isWindow||e.isDocument?"":e.element.css("overflow-x"),i=e.isWindow||e.isDocument?"":e.element.css("overflow-y"),s="scroll"===n||"auto"===n&&e.width<e.element[0].scrollWidth;return{width:"scroll"===i||"auto"===i&&e.height<e.element[0].scrollHeight?t.position.scrollbarWidth():0,height:s?t.position.scrollbarWidth():0}},getWithinInfo:function(e){var n=t(e||window),i=t.isWindow(n[0]);return{element:n,isWindow:i,isDocument:!!n[0]&&9===n[0].nodeType,offset:n.offset()||{left:0,top:0},scrollLeft:n.scrollLeft(),scrollTop:n.scrollTop(),width:i?n.width():n.outerWidth(),height:i?n.height():n.outerHeight()}}},t.fn.position=function(e){if(!e||!e.of)return u.apply(this,arguments);e=t.extend({},e);var n,h,p,g,m,v,b,y,_=t(e.of),w=t.position.getWithinInfo(e.within),C=t.position.getScrollInfo(w),S=(e.collision||"flip").split(" "),x={};return v=9===(y=(b=_)[0]).nodeType?{width:b.width(),height:b.height(),offset:{top:0,left:0}}:t.isWindow(y)?{width:b.width(),height:b.height(),offset:{top:b.scrollTop(),left:b.scrollLeft()}}:y.preventDefault?{width:0,height:0,offset:{top:y.pageY,left:y.pageX}}:{width:b.outerWidth(),height:b.outerHeight(),offset:b.offset()},_[0].preventDefault&&(e.at="left top"),h=v.width,p=v.height,g=v.offset,m=t.extend({},g),t.each(["my","at"],function(){var t,n,i=(e[this]||"").split(" ");1===i.length&&(i=o.test(i[0])?i.concat(["center"]):a.test(i[0])?["center"].concat(i):["center","center"]),i[0]=o.test(i[0])?i[0]:"center",i[1]=a.test(i[1])?i[1]:"center",t=l.exec(i[0]),n=l.exec(i[1]),x[this]=[t?t[0]:0,n?n[0]:0],e[this]=[c.exec(i[0])[0],c.exec(i[1])[0]]}),1===S.length&&(S[1]=S[0]),"right"===e.at[0]?m.left+=h:"center"===e.at[0]&&(m.left+=h/2),"bottom"===e.at[1]?m.top+=p:"center"===e.at[1]&&(m.top+=p/2),n=d(x.at,h,p),m.left+=n[0],m.top+=n[1],this.each(function(){var o,a,l=t(this),c=l.outerWidth(),u=l.outerHeight(),v=f(this,"marginLeft"),b=f(this,"marginTop"),y=c+v+f(this,"marginRight")+C.width,T=u+b+f(this,"marginBottom")+C.height,D=t.extend({},m),E=d(x.my,l.outerWidth(),l.outerHeight());"right"===e.my[0]?D.left-=c:"center"===e.my[0]&&(D.left-=c/2),"bottom"===e.my[1]?D.top-=u:"center"===e.my[1]&&(D.top-=u/2),D.left+=E[0],D.top+=E[1],t.support.offsetFractions||(D.left=r(D.left),D.top=r(D.top)),o={marginLeft:v,marginTop:b},t.each(["left","top"],function(i,s){t.ui.position[S[i]]&&t.ui.position[S[i]][s](D,{targetWidth:h,targetHeight:p,elemWidth:c,elemHeight:u,collisionPosition:o,collisionWidth:y,collisionHeight:T,offset:[n[0]+E[0],n[1]+E[1]],my:e.my,at:e.at,within:w,elem:l})}),e.using&&(a=function(t){var n=g.left-D.left,r=n+h-c,o=g.top-D.top,a=o+p-u,d={target:{element:_,left:g.left,top:g.top,width:h,height:p},element:{element:l,left:D.left,top:D.top,width:c,height:u},horizontal:r<0?"left":n>0?"right":"center",vertical:a<0?"top":o>0?"bottom":"middle"};h<c&&s(n+r)<h&&(d.horizontal="center"),p<u&&s(o+a)<p&&(d.vertical="middle"),i(s(n),s(r))>i(s(o),s(a))?d.important="horizontal":d.important="vertical",e.using.call(this,t,d)}),l.offset(t.extend(D,{using:a}))})},t.ui.position={fit:{left:function(t,e){var n,s=e.within,r=s.isWindow?s.scrollLeft:s.offset.left,o=s.width,a=t.left-e.collisionPosition.marginLeft,l=r-a,c=a+e.collisionWidth-o-r;e.collisionWidth>o?l>0&&c<=0?(n=t.left+l+e.collisionWidth-o-r,t.left+=l-n):t.left=c>0&&l<=0?r:l>c?r+o-e.collisionWidth:r:l>0?t.left+=l:c>0?t.left-=c:t.left=i(t.left-a,t.left)},top:function(t,e){var n,s=e.within,r=s.isWindow?s.scrollTop:s.offset.top,o=e.within.height,a=t.top-e.collisionPosition.marginTop,l=r-a,c=a+e.collisionHeight-o-r;e.collisionHeight>o?l>0&&c<=0?(n=t.top+l+e.collisionHeight-o-r,t.top+=l-n):t.top=c>0&&l<=0?r:l>c?r+o-e.collisionHeight:r:l>0?t.top+=l:c>0?t.top-=c:t.top=i(t.top-a,t.top)}},flip:{left:function(t,e){var n,i,r=e.within,o=r.offset.left+r.scrollLeft,a=r.width,l=r.isWindow?r.scrollLeft:r.offset.left,c=t.left-e.collisionPosition.marginLeft,h=c-l,u=c+e.collisionWidth-a-l,d="left"===e.my[0]?-e.elemWidth:"right"===e.my[0]?e.elemWidth:0,f="left"===e.at[0]?e.targetWidth:"right"===e.at[0]?-e.targetWidth:0,p=-2*e.offset[0];h<0?((n=t.left+d+f+p+e.collisionWidth-a-o)<0||n<s(h))&&(t.left+=d+f+p):u>0&&((i=t.left-e.collisionPosition.marginLeft+d+f+p-l)>0||s(i)<u)&&(t.left+=d+f+p)},top:function(t,e){var n,i,r=e.within,o=r.offset.top+r.scrollTop,a=r.height,l=r.isWindow?r.scrollTop:r.offset.top,c=t.top-e.collisionPosition.marginTop,h=c-l,u=c+e.collisionHeight-a-l,d="top"===e.my[1]?-e.elemHeight:"bottom"===e.my[1]?e.elemHeight:0,f="top"===e.at[1]?e.targetHeight:"bottom"===e.at[1]?-e.targetHeight:0,p=-2*e.offset[1];h<0?(i=t.top+d+f+p+e.collisionHeight-a-o,t.top+d+f+p>h&&(i<0||i<s(h))&&(t.top+=d+f+p)):u>0&&(n=t.top-e.collisionPosition.marginTop+d+f+p-l,t.top+d+f+p>u&&(n>0||s(n)<u)&&(t.top+=d+f+p))}},flipfit:{left:function(){t.ui.position.flip.left.apply(this,arguments),t.ui.position.fit.left.apply(this,arguments)},top:function(){t.ui.position.flip.top.apply(this,arguments),t.ui.position.fit.top.apply(this,arguments)}}},function(){var e,n,i,s,r,o=document.getElementsByTagName("body")[0],a=document.createElement("div");for(r in e=document.createElement(o?"div":"body"),i={visibility:"hidden",width:0,height:0,border:0,margin:0,background:"none"},o&&t.extend(i,{position:"absolute",left:"-1000px",top:"-1000px"}),i)e.style[r]=i[r];e.appendChild(a),(n=o||document.documentElement).insertBefore(e,n.firstChild),a.style.cssText="position: absolute; left: 10.7432222px;",s=t(a).offset().left,t.support.offsetFractions=s>10&&s<11,e.innerHTML="",n.removeChild(e)}()}(jQuery),function(t,e){var n,i="ui-button ui-widget ui-state-default ui-corner-all",s="ui-button-icons-only ui-button-icon-only ui-button-text-icons ui-button-text-icon-primary ui-button-text-icon-secondary ui-button-text-only",r=function(){var e=t(this);setTimeout(function(){e.find(":ui-button").button("refresh")},1)},o=function(e){var n=e.name,i=e.form,s=t([]);return n&&(n=n.replace(/'/g,"\\'"),s=i?t(i).find("[name='"+n+"']"):t("[name='"+n+"']",e.ownerDocument).filter(function(){return!this.form})),s};t.widget("ui.button",{version:"1.10.4",defaultElement:"<button>",options:{disabled:null,text:!0,label:null,icons:{primary:null,secondary:null}},_create:function(){this.element.closest("form").unbind("reset"+this.eventNamespace).bind("reset"+this.eventNamespace,r),"boolean"!=typeof this.options.disabled?this.options.disabled=!!this.element.prop("disabled"):this.element.prop("disabled",this.options.disabled),this._determineButtonType(),this.hasTitle=!!this.buttonElement.attr("title");var e=this,s=this.options,a="checkbox"===this.type||"radio"===this.type,l=a?"":"ui-state-active";null===s.label&&(s.label="input"===this.type?this.buttonElement.val():this.buttonElement.html()),this._hoverable(this.buttonElement),this.buttonElement.addClass(i).attr("role","button").bind("mouseenter"+this.eventNamespace,function(){s.disabled||this===n&&t(this).addClass("ui-state-active")}).bind("mouseleave"+this.eventNamespace,function(){s.disabled||t(this).removeClass(l)}).bind("click"+this.eventNamespace,function(t){s.disabled&&(t.preventDefault(),t.stopImmediatePropagation())}),this._on({focus:function(){this.buttonElement.addClass("ui-state-focus")},blur:function(){this.buttonElement.removeClass("ui-state-focus")}}),a&&this.element.bind("change"+this.eventNamespace,function(){e.refresh()}),"checkbox"===this.type?this.buttonElement.bind("click"+this.eventNamespace,function(){if(s.disabled)return!1}):"radio"===this.type?this.buttonElement.bind("click"+this.eventNamespace,function(){if(s.disabled)return!1;t(this).addClass("ui-state-active"),e.buttonElement.attr("aria-pressed","true");var n=e.element[0];o(n).not(n).map(function(){return t(this).button("widget")[0]}).removeClass("ui-state-active").attr("aria-pressed","false")}):(this.buttonElement.bind("mousedown"+this.eventNamespace,function(){if(s.disabled)return!1;t(this).addClass("ui-state-active"),n=this,e.document.one("mouseup",function(){n=null})}).bind("mouseup"+this.eventNamespace,function(){if(s.disabled)return!1;t(this).removeClass("ui-state-active")}).bind("keydown"+this.eventNamespace,function(e){if(s.disabled)return!1;e.keyCode!==t.ui.keyCode.SPACE&&e.keyCode!==t.ui.keyCode.ENTER||t(this).addClass("ui-state-active")}).bind("keyup"+this.eventNamespace+" blur"+this.eventNamespace,function(){t(this).removeClass("ui-state-active")}),this.buttonElement.is("a")&&this.buttonElement.keyup(function(e){e.keyCode===t.ui.keyCode.SPACE&&t(this).click()})),this._setOption("disabled",s.disabled),this._resetButton()},_determineButtonType:function(){var t,e,n;this.element.is("[type=checkbox]")?this.type="checkbox":this.element.is("[type=radio]")?this.type="radio":this.element.is("input")?this.type="input":this.type="button","checkbox"===this.type||"radio"===this.type?(t=this.element.parents().last(),e="label[for='"+this.element.attr("id")+"']",this.buttonElement=t.find(e),this.buttonElement.length||(t=t.length?t.siblings():this.element.siblings(),this.buttonElement=t.filter(e),this.buttonElement.length||(this.buttonElement=t.find(e))),this.element.addClass("ui-helper-hidden-accessible"),(n=this.element.is(":checked"))&&this.buttonElement.addClass("ui-state-active"),this.buttonElement.prop("aria-pressed",n)):this.buttonElement=this.element},widget:function(){return this.buttonElement},_destroy:function(){this.element.removeClass("ui-helper-hidden-accessible"),this.buttonElement.removeClass(i+" ui-state-active "+s).removeAttr("role").removeAttr("aria-pressed").html(this.buttonElement.find(".ui-button-text").html()),this.hasTitle||this.buttonElement.removeAttr("title")},_setOption:function(t,e){if(this._super(t,e),"disabled"===t)return this.element.prop("disabled",!!e),void(e&&this.buttonElement.removeClass("ui-state-focus"));this._resetButton()},refresh:function(){var e=this.element.is("input, button")?this.element.is(":disabled"):this.element.hasClass("ui-button-disabled");e!==this.options.disabled&&this._setOption("disabled",e),"radio"===this.type?o(this.element[0]).each(function(){t(this).is(":checked")?t(this).button("widget").addClass("ui-state-active").attr("aria-pressed","true"):t(this).button("widget").removeClass("ui-state-active").attr("aria-pressed","false")}):"checkbox"===this.type&&(this.element.is(":checked")?this.buttonElement.addClass("ui-state-active").attr("aria-pressed","true"):this.buttonElement.removeClass("ui-state-active").attr("aria-pressed","false"))},_resetButton:function(){if("input"!==this.type){var e=this.buttonElement.removeClass(s),n=t("<span></span>",this.document[0]).addClass("ui-button-text").html(this.options.label).appendTo(e.empty()).text(),i=this.options.icons,r=i.primary&&i.secondary,o=[];i.primary||i.secondary?(this.options.text&&o.push("ui-button-text-icon"+(r?"s":i.primary?"-primary":"-secondary")),i.primary&&e.prepend("<span class='ui-button-icon-primary ui-icon "+i.primary+"'></span>"),i.secondary&&e.append("<span class='ui-button-icon-secondary ui-icon "+i.secondary+"'></span>"),this.options.text||(o.push(r?"ui-button-icons-only":"ui-button-icon-only"),this.hasTitle||e.attr("title",t.trim(n)))):o.push("ui-button-text-only"),e.addClass(o.join(" "))}else this.options.label&&this.element.val(this.options.label)}}),t.widget("ui.buttonset",{version:"1.10.4",options:{items:"button, input[type=button], input[type=submit], input[type=reset], input[type=checkbox], input[type=radio], a, :data(ui-button)"},_create:function(){this.element.addClass("ui-buttonset")},_init:function(){this.refresh()},_setOption:function(t,e){"disabled"===t&&this.buttons.button("option",t,e),this._super(t,e)},refresh:function(){var e="rtl"===this.element.css("direction");this.buttons=this.element.find(this.options.items).filter(":ui-button").button("refresh").end().not(":ui-button").button().end().map(function(){return t(this).button("widget")[0]}).removeClass("ui-corner-all ui-corner-left ui-corner-right").filter(":first").addClass(e?"ui-corner-right":"ui-corner-left").end().filter(":last").addClass(e?"ui-corner-left":"ui-corner-right").end().end()},_destroy:function(){this.element.removeClass("ui-buttonset"),this.buttons.map(function(){return t(this).button("widget")[0]}).removeClass("ui-corner-left ui-corner-right").end().button("destroy")}})}(jQuery),function(t,e){t.widget("ui.slider",t.ui.mouse,{version:"1.10.4",widgetEventPrefix:"slide",options:{animate:!1,distance:0,max:100,min:0,orientation:"horizontal",range:!1,step:1,value:0,values:null,change:null,slide:null,start:null,stop:null},_create:function(){this._keySliding=!1,this._mouseSliding=!1,this._animateOff=!0,this._handleIndex=null,this._detectOrientation(),this._mouseInit(),this.element.addClass("ui-slider ui-slider-"+this.orientation+" ui-widget ui-widget-content ui-corner-all"),this._refresh(),this._setOption("disabled",this.options.disabled),this._animateOff=!1},_refresh:function(){this._createRange(),this._createHandles(),this._setupEvents(),this._refreshValue()},_createHandles:function(){var e,n,i=this.options,s=this.element.find(".ui-slider-handle").addClass("ui-state-default ui-corner-all"),r=[];for(n=i.values&&i.values.length||1,s.length>n&&(s.slice(n).remove(),s=s.slice(0,n)),e=s.length;e<n;e++)r.push("<a class='ui-slider-handle ui-state-default ui-corner-all' href='#'></a>");this.handles=s.add(t(r.join("")).appendTo(this.element)),this.handle=this.handles.eq(0),this.handles.each(function(e){t(this).data("ui-slider-handle-index",e)})},_createRange:function(){var e=this.options,n="";e.range?(!0===e.range&&(e.values?e.values.length&&2!==e.values.length?e.values=[e.values[0],e.values[0]]:t.isArray(e.values)&&(e.values=e.values.slice(0)):e.values=[this._valueMin(),this._valueMin()]),this.range&&this.range.length?this.range.removeClass("ui-slider-range-min ui-slider-range-max").css({left:"",bottom:""}):(this.range=t("<div></div>").appendTo(this.element),n="ui-slider-range ui-widget-header ui-corner-all"),this.range.addClass(n+("min"===e.range||"max"===e.range?" ui-slider-range-"+e.range:""))):(this.range&&this.range.remove(),this.range=null)},_setupEvents:function(){var t=this.handles.add(this.range).filter("a");this._off(t),this._on(t,this._handleEvents),this._hoverable(t),this._focusable(t)},_destroy:function(){this.handles.remove(),this.range&&this.range.remove(),this.element.removeClass("ui-slider ui-slider-horizontal ui-slider-vertical ui-widget ui-widget-content ui-corner-all"),this._mouseDestroy()},_mouseCapture:function(e){var n,i,s,r,o,a,l,c=this,h=this.options;return!h.disabled&&(this.elementSize={width:this.element.outerWidth(),height:this.element.outerHeight()},this.elementOffset=this.element.offset(),n={x:e.pageX,y:e.pageY},i=this._normValueFromMouse(n),s=this._valueMax()-this._valueMin()+1,this.handles.each(function(e){var n=Math.abs(i-c.values(e));(s>n||s===n&&(e===c._lastChangedValue||c.values(e)===h.min))&&(s=n,r=t(this),o=e)}),!1!==this._start(e,o)&&(this._mouseSliding=!0,this._handleIndex=o,r.addClass("ui-state-active").focus(),a=r.offset(),l=!t(e.target).parents().addBack().is(".ui-slider-handle"),this._clickOffset=l?{left:0,top:0}:{left:e.pageX-a.left-r.width()/2,top:e.pageY-a.top-r.height()/2-(parseInt(r.css("borderTopWidth"),10)||0)-(parseInt(r.css("borderBottomWidth"),10)||0)+(parseInt(r.css("marginTop"),10)||0)},this.handles.hasClass("ui-state-hover")||this._slide(e,o,i),this._animateOff=!0,!0))},_mouseStart:function(){return!0},_mouseDrag:function(t){var e={x:t.pageX,y:t.pageY},n=this._normValueFromMouse(e);return this._slide(t,this._handleIndex,n),!1},_mouseStop:function(t){return this.handles.removeClass("ui-state-active"),this._mouseSliding=!1,this._stop(t,this._handleIndex),this._change(t,this._handleIndex),this._handleIndex=null,this._clickOffset=null,this._animateOff=!1,!1},_detectOrientation:function(){this.orientation="vertical"===this.options.orientation?"vertical":"horizontal"},_normValueFromMouse:function(t){var e,n,i,s,r;return"horizontal"===this.orientation?(e=this.elementSize.width,n=t.x-this.elementOffset.left-(this._clickOffset?this._clickOffset.left:0)):(e=this.elementSize.height,n=t.y-this.elementOffset.top-(this._clickOffset?this._clickOffset.top:0)),(i=n/e)>1&&(i=1),i<0&&(i=0),"vertical"===this.orientation&&(i=1-i),s=this._valueMax()-this._valueMin(),r=this._valueMin()+i*s,this._trimAlignValue(r)},_start:function(t,e){var n={handle:this.handles[e],value:this.value()};return this.options.values&&this.options.values.length&&(n.value=this.values(e),n.values=this.values()),this._trigger("start",t,n)},_slide:function(t,e,n){var i,s,r;this.options.values&&this.options.values.length?(i=this.values(e?0:1),2===this.options.values.length&&!0===this.options.range&&(0===e&&n>i||1===e&&n<i)&&(n=i),n!==this.values(e)&&((s=this.values())[e]=n,r=this._trigger("slide",t,{handle:this.handles[e],value:n,values:s}),i=this.values(e?0:1),!1!==r&&this.values(e,n))):n!==this.value()&&!1!==(r=this._trigger("slide",t,{handle:this.handles[e],value:n}))&&this.value(n)},_stop:function(t,e){var n={handle:this.handles[e],value:this.value()};this.options.values&&this.options.values.length&&(n.value=this.values(e),n.values=this.values()),this._trigger("stop",t,n)},_change:function(t,e){if(!this._keySliding&&!this._mouseSliding){var n={handle:this.handles[e],value:this.value()};this.options.values&&this.options.values.length&&(n.value=this.values(e),n.values=this.values()),this._lastChangedValue=e,this._trigger("change",t,n)}},value:function(t){return arguments.length?(this.options.value=this._trimAlignValue(t),this._refreshValue(),void this._change(null,0)):this._value()},values:function(e,n){var i,s,r;if(arguments.length>1)return this.options.values[e]=this._trimAlignValue(n),this._refreshValue(),void this._change(null,e);if(!arguments.length)return this._values();if(!t.isArray(arguments[0]))return this.options.values&&this.options.values.length?this._values(e):this.value();for(i=this.options.values,s=arguments[0],r=0;r<i.length;r+=1)i[r]=this._trimAlignValue(s[r]),this._change(null,r);this._refreshValue()},_setOption:function(e,n){var i,s=0;switch("range"===e&&!0===this.options.range&&("min"===n?(this.options.value=this._values(0),this.options.values=null):"max"===n&&(this.options.value=this._values(this.options.values.length-1),this.options.values=null)),t.isArray(this.options.values)&&(s=this.options.values.length),t.Widget.prototype._setOption.apply(this,arguments),e){case"orientation":this._detectOrientation(),this.element.removeClass("ui-slider-horizontal ui-slider-vertical").addClass("ui-slider-"+this.orientation),this._refreshValue();break;case"value":this._animateOff=!0,this._refreshValue(),this._change(null,0),this._animateOff=!1;break;case"values":for(this._animateOff=!0,this._refreshValue(),i=0;i<s;i+=1)this._change(null,i);this._animateOff=!1;break;case"min":case"max":this._animateOff=!0,this._refreshValue(),this._animateOff=!1;break;case"range":this._animateOff=!0,this._refresh(),this._animateOff=!1}},_value:function(){var t=this.options.value;return t=this._trimAlignValue(t)},_values:function(t){var e,n,i;if(arguments.length)return e=this.options.values[t],e=this._trimAlignValue(e);if(this.options.values&&this.options.values.length){for(n=this.options.values.slice(),i=0;i<n.length;i+=1)n[i]=this._trimAlignValue(n[i]);return n}return[]},_trimAlignValue:function(t){if(t<=this._valueMin())return this._valueMin();if(t>=this._valueMax())return this._valueMax();var e=this.options.step>0?this.options.step:1,n=(t-this._valueMin())%e,i=t-n;return 2*Math.abs(n)>=e&&(i+=n>0?e:-e),parseFloat(i.toFixed(5))},_valueMin:function(){return this.options.min},_valueMax:function(){return this.options.max},_refreshValue:function(){var e,n,i,s,r,o=this.options.range,a=this.options,l=this,c=!this._animateOff&&a.animate,h={};this.options.values&&this.options.values.length?this.handles.each(function(i){n=(l.values(i)-l._valueMin())/(l._valueMax()-l._valueMin())*100,h["horizontal"===l.orientation?"left":"bottom"]=n+"%",t(this).stop(1,1)[c?"animate":"css"](h,a.animate),!0===l.options.range&&("horizontal"===l.orientation?(0===i&&l.range.stop(1,1)[c?"animate":"css"]({left:n+"%"},a.animate),1===i&&l.range[c?"animate":"css"]({width:n-e+"%"},{queue:!1,duration:a.animate})):(0===i&&l.range.stop(1,1)[c?"animate":"css"]({bottom:n+"%"},a.animate),1===i&&l.range[c?"animate":"css"]({height:n-e+"%"},{queue:!1,duration:a.animate}))),e=n}):(i=this.value(),s=this._valueMin(),r=this._valueMax(),n=r!==s?(i-s)/(r-s)*100:0,h["horizontal"===this.orientation?"left":"bottom"]=n+"%",this.handle.stop(1,1)[c?"animate":"css"](h,a.animate),"min"===o&&"horizontal"===this.orientation&&this.range.stop(1,1)[c?"animate":"css"]({width:n+"%"},a.animate),"max"===o&&"horizontal"===this.orientation&&this.range[c?"animate":"css"]({width:100-n+"%"},{queue:!1,duration:a.animate}),"min"===o&&"vertical"===this.orientation&&this.range.stop(1,1)[c?"animate":"css"]({height:n+"%"},a.animate),"max"===o&&"vertical"===this.orientation&&this.range[c?"animate":"css"]({height:100-n+"%"},{queue:!1,duration:a.animate}))},_handleEvents:{keydown:function(e){var n,i,s,r=t(e.target).data("ui-slider-handle-index");switch(e.keyCode){case t.ui.keyCode.HOME:case t.ui.keyCode.END:case t.ui.keyCode.PAGE_UP:case t.ui.keyCode.PAGE_DOWN:case t.ui.keyCode.UP:case t.ui.keyCode.RIGHT:case t.ui.keyCode.DOWN:case t.ui.keyCode.LEFT:if(e.preventDefault(),!this._keySliding&&(this._keySliding=!0,t(e.target).addClass("ui-state-active"),!1===this._start(e,r)))return}switch(s=this.options.step,n=i=this.options.values&&this.options.values.length?this.values(r):this.value(),e.keyCode){case t.ui.keyCode.HOME:i=this._valueMin();break;case t.ui.keyCode.END:i=this._valueMax();break;case t.ui.keyCode.PAGE_UP:i=this._trimAlignValue(n+(this._valueMax()-this._valueMin())/5);break;case t.ui.keyCode.PAGE_DOWN:i=this._trimAlignValue(n-(this._valueMax()-this._valueMin())/5);break;case t.ui.keyCode.UP:case t.ui.keyCode.RIGHT:if(n===this._valueMax())return;i=this._trimAlignValue(n+s);break;case t.ui.keyCode.DOWN:case t.ui.keyCode.LEFT:if(n===this._valueMin())return;i=this._trimAlignValue(n-s)}this._slide(e,r,i)},click:function(t){t.preventDefault()},keyup:function(e){var n=t(e.target).data("ui-slider-handle-index");this._keySliding&&(this._keySliding=!1,this._stop(e,n),this._change(e,n),t(e.target).removeClass("ui-state-active"))}}})}(jQuery),function(t,e){var n;t.effects={effect:{}},function(t,e){var n,i=/^([\-+])=\s*(\d+\.?\d*)/,s=[{re:/rgba?\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,parse:function(t){return[t[1],t[2],t[3],t[4]]}},{re:/rgba?\(\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,parse:function(t){return[2.55*t[1],2.55*t[2],2.55*t[3],t[4]]}},{re:/#([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})/,parse:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/#([a-f0-9])([a-f0-9])([a-f0-9])/,parse:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}},{re:/hsla?\(\s*(\d+(?:\.\d+)?)\s*,\s*(\d+(?:\.\d+)?)\%\s*,\s*(\d+(?:\.\d+)?)\%\s*(?:,\s*(\d?(?:\.\d+)?)\s*)?\)/,space:"hsla",parse:function(t){return[t[1],t[2]/100,t[3]/100,t[4]]}}],r=t.Color=function(e,n,i,s){return new t.Color.fn.parse(e,n,i,s)},o={rgba:{props:{red:{idx:0,type:"byte"},green:{idx:1,type:"byte"},blue:{idx:2,type:"byte"}}},hsla:{props:{hue:{idx:0,type:"degrees"},saturation:{idx:1,type:"percent"},lightness:{idx:2,type:"percent"}}}},a={byte:{floor:!0,max:255},percent:{max:1},degrees:{mod:360,floor:!0}},l=r.support={},c=t("<p>")[0],h=t.each;function u(t,e,n){var i=a[e.type]||{};return null==t?n||!e.def?null:e.def:(t=i.floor?~~t:parseFloat(t),isNaN(t)?e.def:i.mod?(t+i.mod)%i.mod:0>t?0:i.max<t?i.max:t)}function d(e){var i=r(),a=i._rgba=[];return e=e.toLowerCase(),h(s,function(t,n){var s,r=n.re.exec(e),l=r&&n.parse(r),c=n.space||"rgba";if(l)return s=i[c](l),i[o[c].cache]=s[o[c].cache],a=i._rgba=s._rgba,!1}),a.length?("0,0,0,0"===a.join()&&t.extend(a,n.transparent),i):n[e]}function f(t,e,n){return 6*(n=(n+1)%1)<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}c.style.cssText="background-color:rgba(1,1,1,.5)",l.rgba=c.style.backgroundColor.indexOf("rgba")>-1,h(o,function(t,e){e.cache="_"+t,e.props.alpha={idx:3,type:"percent",def:1}}),r.fn=t.extend(r.prototype,{parse:function(e,i,s,a){if(void 0===e)return this._rgba=[null,null,null,null],this;(e.jquery||e.nodeType)&&(e=t(e).css(i),i=void 0);var l=this,c=t.type(e),f=this._rgba=[];return void 0!==i&&(e=[e,i,s,a],c="array"),"string"===c?this.parse(d(e)||n._default):"array"===c?(h(o.rgba.props,function(t,n){f[n.idx]=u(e[n.idx],n)}),this):"object"===c?(h(o,e instanceof r?function(t,n){e[n.cache]&&(l[n.cache]=e[n.cache].slice())}:function(n,i){var s=i.cache;h(i.props,function(t,n){if(!l[s]&&i.to){if("alpha"===t||null==e[t])return;l[s]=i.to(l._rgba)}l[s][n.idx]=u(e[t],n,!0)}),l[s]&&t.inArray(null,l[s].slice(0,3))<0&&(l[s][3]=1,i.from&&(l._rgba=i.from(l[s])))}),this):void 0},is:function(t){var e=r(t),n=!0,i=this;return h(o,function(t,s){var r,o=e[s.cache];return o&&(r=i[s.cache]||s.to&&s.to(i._rgba)||[],h(s.props,function(t,e){if(null!=o[e.idx])return n=o[e.idx]===r[e.idx]})),n}),n},_space:function(){var t=[],e=this;return h(o,function(n,i){e[i.cache]&&t.push(n)}),t.pop()},transition:function(t,e){var n=r(t),i=n._space(),s=o[i],l=0===this.alpha()?r("transparent"):this,c=l[s.cache]||s.to(l._rgba),d=c.slice();return n=n[s.cache],h(s.props,function(t,i){var s=i.idx,r=c[s],o=n[s],l=a[i.type]||{};null!==o&&(null===r?d[s]=o:(l.mod&&(o-r>l.mod/2?r+=l.mod:r-o>l.mod/2&&(r-=l.mod)),d[s]=u((o-r)*e+r,i)))}),this[i](d)},blend:function(e){if(1===this._rgba[3])return this;var n=this._rgba.slice(),i=n.pop(),s=r(e)._rgba;return r(t.map(n,function(t,e){return(1-i)*s[e]+i*t}))},toRgbaString:function(){var e="rgba(",n=t.map(this._rgba,function(t,e){return null==t?e>2?1:0:t});return 1===n[3]&&(n.pop(),e="rgb("),e+n.join()+")"},toHslaString:function(){var e="hsla(",n=t.map(this.hsla(),function(t,e){return null==t&&(t=e>2?1:0),e&&e<3&&(t=Math.round(100*t)+"%"),t});return 1===n[3]&&(n.pop(),e="hsl("),e+n.join()+")"},toHexString:function(e){var n=this._rgba.slice(),i=n.pop();return e&&n.push(~~(255*i)),"#"+t.map(n,function(t){return 1===(t=(t||0).toString(16)).length?"0"+t:t}).join("")},toString:function(){return 0===this._rgba[3]?"transparent":this.toRgbaString()}}),r.fn.parse.prototype=r.fn,o.hsla.to=function(t){if(null==t[0]||null==t[1]||null==t[2])return[null,null,null,t[3]];var e,n,i=t[0]/255,s=t[1]/255,r=t[2]/255,o=t[3],a=Math.max(i,s,r),l=Math.min(i,s,r),c=a-l,h=a+l,u=.5*h;return e=l===a?0:i===a?60*(s-r)/c+360:s===a?60*(r-i)/c+120:60*(i-s)/c+240,n=0===c?0:u<=.5?c/h:c/(2-h),[Math.round(e)%360,n,u,null==o?1:o]},o.hsla.from=function(t){if(null==t[0]||null==t[1]||null==t[2])return[null,null,null,t[3]];var e=t[0]/360,n=t[1],i=t[2],s=t[3],r=i<=.5?i*(1+n):i+n-i*n,o=2*i-r;return[Math.round(255*f(o,r,e+1/3)),Math.round(255*f(o,r,e)),Math.round(255*f(o,r,e-1/3)),s]},h(o,function(e,n){var s=n.props,o=n.cache,a=n.to,l=n.from;r.fn[e]=function(e){if(a&&!this[o]&&(this[o]=a(this._rgba)),void 0===e)return this[o].slice();var n,i=t.type(e),c="array"===i||"object"===i?e:arguments,d=this[o].slice();return h(s,function(t,e){var n=c["object"===i?t:e.idx];null==n&&(n=d[e.idx]),d[e.idx]=u(n,e)}),l?((n=r(l(d)))[o]=d,n):r(d)},h(s,function(n,s){r.fn[n]||(r.fn[n]=function(r){var o,a=t.type(r),l="alpha"===n?this._hsla?"hsla":"rgba":e,c=this[l](),h=c[s.idx];return"undefined"===a?h:("function"===a&&(r=r.call(this,h),a=t.type(r)),null==r&&s.empty?this:("string"===a&&(o=i.exec(r))&&(r=h+parseFloat(o[2])*("+"===o[1]?1:-1)),c[s.idx]=r,this[l](c)))})})}),r.hook=function(e){var n=e.split(" ");h(n,function(e,n){t.cssHooks[n]={set:function(e,i){var s,o,a="";if("transparent"!==i&&("string"!==t.type(i)||(s=d(i)))){if(i=r(s||i),!l.rgba&&1!==i._rgba[3]){for(o="backgroundColor"===n?e.parentNode:e;(""===a||"transparent"===a)&&o&&o.style;)try{a=t.css(o,"backgroundColor"),o=o.parentNode}catch(t){}i=i.blend(a&&"transparent"!==a?a:"_default")}i=i.toRgbaString()}try{e.style[n]=i}catch(t){}}},t.fx.step[n]=function(e){e.colorInit||(e.start=r(e.elem,n),e.end=r(e.end),e.colorInit=!0),t.cssHooks[n].set(e.elem,e.start.transition(e.end,e.pos))}})},r.hook("backgroundColor borderBottomColor borderLeftColor borderRightColor borderTopColor color columnRuleColor outlineColor textDecorationColor textEmphasisColor"),t.cssHooks.borderColor={expand:function(t){var e={};return h(["Top","Right","Bottom","Left"],function(n,i){e["border"+i+"Color"]=t}),e}},n=t.Color.names={aqua:"#00ffff",black:"#000000",blue:"#0000ff",fuchsia:"#ff00ff",gray:"#808080",green:"#008000",lime:"#00ff00",maroon:"#800000",navy:"#000080",olive:"#808000",purple:"#800080",red:"#ff0000",silver:"#c0c0c0",teal:"#008080",white:"#ffffff",yellow:"#ffff00",transparent:[null,null,null,0],_default:"#ffffff"}}(jQuery),function(){var e,n=["add","remove","toggle"],i={border:1,borderBottom:1,borderColor:1,borderLeft:1,borderRight:1,borderTop:1,borderWidth:1,margin:1,padding:1};function s(e){var n,i,s=e.ownerDocument.defaultView?e.ownerDocument.defaultView.getComputedStyle(e,null):e.currentStyle,r={};if(s&&s.length&&s[0]&&s[s[0]])for(i=s.length;i--;)"string"==typeof s[n=s[i]]&&(r[t.camelCase(n)]=s[n]);else for(n in s)"string"==typeof s[n]&&(r[n]=s[n]);return r}t.each(["borderLeftStyle","borderRightStyle","borderBottomStyle","borderTopStyle"],function(e,n){t.fx.step[n]=function(t){("none"!==t.end&&!t.setAttr||1===t.pos&&!t.setAttr)&&(jQuery.style(t.elem,n,t.end),t.setAttr=!0)}}),t.fn.addBack||(t.fn.addBack=function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}),t.effects.animateClass=function(e,r,o,a){var l=t.speed(r,o,a);return this.queue(function(){var r,o=t(this),a=o.attr("class")||"",c=l.children?o.find("*").addBack():o;c=c.map(function(){return{el:t(this),start:s(this)}}),(r=function(){t.each(n,function(t,n){e[n]&&o[n+"Class"](e[n])})})(),c=c.map(function(){return this.end=s(this.el[0]),this.diff=function(e,n){var s,r,o={};for(s in n)r=n[s],e[s]!==r&&(i[s]||!t.fx.step[s]&&isNaN(parseFloat(r))||(o[s]=r));return o}(this.start,this.end),this}),o.attr("class",a),c=c.map(function(){var e=this,n=t.Deferred(),i=t.extend({},l,{queue:!1,complete:function(){n.resolve(e)}});return this.el.animate(this.diff,i),n.promise()}),t.when.apply(t,c.get()).done(function(){r(),t.each(arguments,function(){var e=this.el;t.each(this.diff,function(t){e.css(t,"")})}),l.complete.call(o[0])})})},t.fn.extend({addClass:(e=t.fn.addClass,function(n,i,s,r){return i?t.effects.animateClass.call(this,{add:n},i,s,r):e.apply(this,arguments)}),removeClass:function(e){return function(n,i,s,r){return arguments.length>1?t.effects.animateClass.call(this,{remove:n},i,s,r):e.apply(this,arguments)}}(t.fn.removeClass),toggleClass:function(e){return function(n,i,s,r,o){return"boolean"==typeof i||void 0===i?s?t.effects.animateClass.call(this,i?{add:n}:{remove:n},s,r,o):e.apply(this,arguments):t.effects.animateClass.call(this,{toggle:n},i,s,r)}}(t.fn.toggleClass),switchClass:function(e,n,i,s,r){return t.effects.animateClass.call(this,{add:n,remove:e},i,s,r)}})}(),function(){function e(e,n,i,s){return t.isPlainObject(e)&&(n=e,e=e.effect),e={effect:e},null==n&&(n={}),t.isFunction(n)&&(s=n,i=null,n={}),("number"==typeof n||t.fx.speeds[n])&&(s=i,i=n,n={}),t.isFunction(i)&&(s=i,i=null),n&&t.extend(e,n),i=i||n.duration,e.duration=t.fx.off?0:"number"==typeof i?i:i in t.fx.speeds?t.fx.speeds[i]:t.fx.speeds._default,e.complete=s||n.complete,e}function n(e){return!(e&&"number"!=typeof e&&!t.fx.speeds[e])||("string"==typeof e&&!t.effects.effect[e]||(!!t.isFunction(e)||"object"==typeof e&&!e.effect))}var i;t.extend(t.effects,{version:"1.10.4",save:function(t,e){for(var n=0;n<e.length;n++)null!==e[n]&&t.data("ui-effects-"+e[n],t[0].style[e[n]])},restore:function(t,e){var n,i;for(i=0;i<e.length;i++)null!==e[i]&&(void 0===(n=t.data("ui-effects-"+e[i]))&&(n=""),t.css(e[i],n))},setMode:function(t,e){return"toggle"===e&&(e=t.is(":hidden")?"show":"hide"),e},getBaseline:function(t,e){var n,i;switch(t[0]){case"top":n=0;break;case"middle":n=.5;break;case"bottom":n=1;break;default:n=t[0]/e.height}switch(t[1]){case"left":i=0;break;case"center":i=.5;break;case"right":i=1;break;default:i=t[1]/e.width}return{x:i,y:n}},createWrapper:function(e){if(e.parent().is(".ui-effects-wrapper"))return e.parent();var n={width:e.outerWidth(!0),height:e.outerHeight(!0),float:e.css("float")},i=t("<div></div>").addClass("ui-effects-wrapper").css({fontSize:"100%",background:"transparent",border:"none",margin:0,padding:0}),s={width:e.width(),height:e.height()},r=document.activeElement;try{r.id}catch(t){r=document.body}return e.wrap(i),(e[0]===r||t.contains(e[0],r))&&t(r).focus(),i=e.parent(),"static"===e.css("position")?(i.css({position:"relative"}),e.css({position:"relative"})):(t.extend(n,{position:e.css("position"),zIndex:e.css("z-index")}),t.each(["top","left","bottom","right"],function(t,i){n[i]=e.css(i),isNaN(parseInt(n[i],10))&&(n[i]="auto")}),e.css({position:"relative",top:0,left:0,right:"auto",bottom:"auto"})),e.css(s),i.css(n).show()},removeWrapper:function(e){var n=document.activeElement;return e.parent().is(".ui-effects-wrapper")&&(e.parent().replaceWith(e),(e[0]===n||t.contains(e[0],n))&&t(n).focus()),e},setTransition:function(e,n,i,s){return s=s||{},t.each(n,function(t,n){var r=e.cssUnit(n);r[0]>0&&(s[n]=r[0]*i+r[1])}),s}}),t.fn.extend({effect:function(){var n=e.apply(this,arguments),i=n.mode,s=n.queue,r=t.effects.effect[n.effect];if(t.fx.off||!r)return i?this[i](n.duration,n.complete):this.each(function(){n.complete&&n.complete.call(this)});function o(e){var i=t(this),s=n.complete,o=n.mode;function a(){t.isFunction(s)&&s.call(i[0]),t.isFunction(e)&&e()}(i.is(":hidden")?"hide"===o:"show"===o)?(i[o](),a()):r.call(i[0],n,a)}return!1===s?this.each(o):this.queue(s||"fx",o)},show:(i=t.fn.show,function(t){if(n(t))return i.apply(this,arguments);var s=e.apply(this,arguments);return s.mode="show",this.effect.call(this,s)}),hide:function(t){return function(i){if(n(i))return t.apply(this,arguments);var s=e.apply(this,arguments);return s.mode="hide",this.effect.call(this,s)}}(t.fn.hide),toggle:function(t){return function(i){if(n(i)||"boolean"==typeof i)return t.apply(this,arguments);var s=e.apply(this,arguments);return s.mode="toggle",this.effect.call(this,s)}}(t.fn.toggle),cssUnit:function(e){var n=this.css(e),i=[];return t.each(["em","px","%","pt"],function(t,e){n.indexOf(e)>0&&(i=[parseFloat(n),e])}),i}})}(),n={},t.each(["Quad","Cubic","Quart","Quint","Expo"],function(t,e){n[e]=function(e){return Math.pow(e,t+2)}}),t.extend(n,{Sine:function(t){return 1-Math.cos(t*Math.PI/2)},Circ:function(t){return 1-Math.sqrt(1-t*t)},Elastic:function(t){return 0===t||1===t?t:-Math.pow(2,8*(t-1))*Math.sin((80*(t-1)-7.5)*Math.PI/15)},Back:function(t){return t*t*(3*t-2)},Bounce:function(t){for(var e,n=4;t<((e=Math.pow(2,--n))-1)/11;);return 1/Math.pow(4,3-n)-7.5625*Math.pow((3*e-2)/22-t,2)}}),t.each(n,function(e,n){t.easing["easeIn"+e]=n,t.easing["easeOut"+e]=function(t){return 1-n(1-t)},t.easing["easeInOut"+e]=function(t){return t<.5?n(2*t)/2:1-n(-2*t+2)/2}})}(jQuery),function(t){if(t.support.touch="ontouchend"in document,t.support.touch){var e,n=t.ui.mouse.prototype,i=n._mouseInit;n._touchStart=function(t){!e&&this._mouseCapture(t.originalEvent.changedTouches[0])&&(e=!0,this._touchMoved=!1,s(t,"mouseover"),s(t,"mousemove"),s(t,"mousedown"))},n._touchMove=function(t){e&&(this._touchMoved=!0,s(t,"mousemove"))},n._touchEnd=function(t){e&&(s(t,"mouseup"),s(t,"mouseout"),this._touchMoved||s(t,"click"),e=!1)},n._mouseInit=function(){this.element.bind("touchstart",t.proxy(this,"_touchStart")).bind("touchmove",t.proxy(this,"_touchMove")).bind("touchend",t.proxy(this,"_touchEnd")),i.call(this)}}function s(t,e){if(!(t.originalEvent.touches.length>1)){t.preventDefault();var n=t.originalEvent.changedTouches[0],i=document.createEvent("MouseEvents");i.initMouseEvent(e,!0,!0,window,1,n.screenX,n.screenY,n.clientX,n.clientY,!1,!1,!1,!1,0,null),t.target.dispatchEvent(i)}}}(jQuery),function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("jquery"),require("popper.js")):"function"==typeof define&&define.amd?define(["exports","jquery","popper.js"],e):e(t.bootstrap={},t.jQuery,t.Popper)}(this,function(t,e,n){"use strict";function i(t,e){for(var n=0;n<e.length;n++){var i=e[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function s(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),t}function r(){return(r=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i])}return t}).apply(this,arguments)}e=e&&e.hasOwnProperty("default")?e.default:e,n=n&&n.hasOwnProperty("default")?n.default:n;var o=function(t){var e=!1;function n(e){var n=this,s=!1;return t(this).one(i.TRANSITION_END,function(){s=!0}),setTimeout(function(){s||i.triggerTransitionEnd(n)},e),this}var i={TRANSITION_END:"bsTransitionEnd",getUID:function(t){do{t+=~~(1e6*Math.random())}while(document.getElementById(t));return t},getSelectorFromElement:function(e){var n=e.getAttribute("data-target");n&&"#"!==n||(n=e.getAttribute("href")||""),"#"===n.charAt(0)&&(n=function(e){return e="function"==typeof t.escapeSelector?t.escapeSelector(e).substr(1):e.replace(/(:|\.|\[|\]|,|=|@)/g,"\\$1")}(n));try{return t(document).find(n).length>0?n:null}catch(t){return null}},reflow:function(t){return t.offsetHeight},triggerTransitionEnd:function(n){t(n).trigger(e.end)},supportsTransitionEnd:function(){return Boolean(e)},isElement:function(t){return(t[0]||t).nodeType},typeCheckConfig:function(t,e,n){for(var s in n)if(Object.prototype.hasOwnProperty.call(n,s)){var r=n[s],o=e[s],a=o&&i.isElement(o)?"element":(l=o,{}.toString.call(l).match(/\s([a-zA-Z]+)/)[1].toLowerCase());if(!new RegExp(r).test(a))throw new Error(t.toUpperCase()+': Option "'+s+'" provided type "'+a+'" but expected type "'+r+'".')}var l}};return e=("undefined"==typeof window||!window.QUnit)&&{end:"transitionend"},t.fn.emulateTransitionEnd=n,i.supportsTransitionEnd()&&(t.event.special[i.TRANSITION_END]={bindType:e.end,delegateType:e.end,handle:function(e){if(t(e.target).is(this))return e.handleObj.handler.apply(this,arguments)}}),i}(e),a=function(t){var e=t.fn.alert,n={CLOSE:"close.bs.alert",CLOSED:"closed.bs.alert",CLICK_DATA_API:"click.bs.alert.data-api"},i="alert",r="fade",a="show",l=function(){function e(t){this._element=t}var l=e.prototype;return l.close=function(t){t=t||this._element;var e=this._getRootElement(t);this._triggerCloseEvent(e).isDefaultPrevented()||this._removeElement(e)},l.dispose=function(){t.removeData(this._element,"bs.alert"),this._element=null},l._getRootElement=function(e){var n=o.getSelectorFromElement(e),s=!1;return n&&(s=t(n)[0]),s||(s=t(e).closest("."+i)[0]),s},l._triggerCloseEvent=function(e){var i=t.Event(n.CLOSE);return t(e).trigger(i),i},l._removeElement=function(e){var n=this;t(e).removeClass(a),o.supportsTransitionEnd()&&t(e).hasClass(r)?t(e).one(o.TRANSITION_END,function(t){return n._destroyElement(e,t)}).emulateTransitionEnd(150):this._destroyElement(e)},l._destroyElement=function(e){t(e).detach().trigger(n.CLOSED).remove()},e._jQueryInterface=function(n){return this.each(function(){var i=t(this),s=i.data("bs.alert");s||(s=new e(this),i.data("bs.alert",s)),"close"===n&&s[n](this)})},e._handleDismiss=function(t){return function(e){e&&e.preventDefault(),t.close(this)}},s(e,null,[{key:"VERSION",get:function(){return"4.0.0"}}]),e}();return t(document).on(n.CLICK_DATA_API,'[data-dismiss="alert"]',l._handleDismiss(new l)),t.fn.alert=l._jQueryInterface,t.fn.alert.Constructor=l,t.fn.alert.noConflict=function(){return t.fn.alert=e,l._jQueryInterface},l}(e),l=function(t){var e="button",n=t.fn[e],i="active",r="btn",o="focus",a='[data-toggle^="button"]',l='[data-toggle="buttons"]',c="input",h=".active",u=".btn",d={CLICK_DATA_API:"click.bs.button.data-api",FOCUS_BLUR_DATA_API:"focus.bs.button.data-api blur.bs.button.data-api"},f=function(){function e(t){this._element=t}var n=e.prototype;return n.toggle=function(){var e=!0,n=!0,s=t(this._element).closest(l)[0];if(s){var r=t(this._element).find(c)[0];if(r){if("radio"===r.type)if(r.checked&&t(this._element).hasClass(i))e=!1;else{var o=t(s).find(h)[0];o&&t(o).removeClass(i)}if(e){if(r.hasAttribute("disabled")||s.hasAttribute("disabled")||r.classList.contains("disabled")||s.classList.contains("disabled"))return;r.checked=!t(this._element).hasClass(i),t(r).trigger("change")}r.focus(),n=!1}}n&&this._element.setAttribute("aria-pressed",!t(this._element).hasClass(i)),e&&t(this._element).toggleClass(i)},n.dispose=function(){t.removeData(this._element,"bs.button"),this._element=null},e._jQueryInterface=function(n){return this.each(function(){var i=t(this).data("bs.button");i||(i=new e(this),t(this).data("bs.button",i)),"toggle"===n&&i[n]()})},s(e,null,[{key:"VERSION",get:function(){return"4.0.0"}}]),e}();return t(document).on(d.CLICK_DATA_API,a,function(e){e.preventDefault();var n=e.target;t(n).hasClass(r)||(n=t(n).closest(u)),f._jQueryInterface.call(t(n),"toggle")}).on(d.FOCUS_BLUR_DATA_API,a,function(e){var n=t(e.target).closest(u)[0];t(n).toggleClass(o,/^focus(in)?$/.test(e.type))}),t.fn[e]=f._jQueryInterface,t.fn[e].Constructor=f,t.fn[e].noConflict=function(){return t.fn[e]=n,f._jQueryInterface},f}(e),c=function(t){var e="carousel",n="bs.carousel",i="."+n,a=t.fn[e],l={interval:5e3,keyboard:!0,slide:!1,pause:"hover",wrap:!0},c={interval:"(number|boolean)",keyboard:"boolean",slide:"(boolean|string)",pause:"(string|boolean)",wrap:"boolean"},h="next",u="prev",d="left",f="right",p={SLIDE:"slide"+i,SLID:"slid"+i,KEYDOWN:"keydown"+i,MOUSEENTER:"mouseenter"+i,MOUSELEAVE:"mouseleave"+i,TOUCHEND:"touchend"+i,LOAD_DATA_API:"load.bs.carousel.data-api",CLICK_DATA_API:"click.bs.carousel.data-api"},g="carousel",m="active",v="slide",b="carousel-item-right",y="carousel-item-left",_="carousel-item-next",w="carousel-item-prev",C={ACTIVE:".active",ACTIVE_ITEM:".active.carousel-item",ITEM:".carousel-item",NEXT_PREV:".carousel-item-next, .carousel-item-prev",INDICATORS:".carousel-indicators",DATA_SLIDE:"[data-slide], [data-slide-to]",DATA_RIDE:'[data-ride="carousel"]'},S=function(){function a(e,n){this._items=null,this._interval=null,this._activeElement=null,this._isPaused=!1,this._isSliding=!1,this.touchTimeout=null,this._config=this._getConfig(n),this._element=t(e)[0],this._indicatorsElement=t(this._element).find(C.INDICATORS)[0],this._addEventListeners()}var S=a.prototype;return S.next=function(){this._isSliding||this._slide(h)},S.nextWhenVisible=function(){!document.hidden&&t(this._element).is(":visible")&&"hidden"!==t(this._element).css("visibility")&&this.next()},S.prev=function(){this._isSliding||this._slide(u)},S.pause=function(e){e||(this._isPaused=!0),t(this._element).find(C.NEXT_PREV)[0]&&o.supportsTransitionEnd()&&(o.triggerTransitionEnd(this._element),this.cycle(!0)),clearInterval(this._interval),this._interval=null},S.cycle=function(t){t||(this._isPaused=!1),this._interval&&(clearInterval(this._interval),this._interval=null),this._config.interval&&!this._isPaused&&(this._interval=setInterval((document.visibilityState?this.nextWhenVisible:this.next).bind(this),this._config.interval))},S.to=function(e){var n=this;this._activeElement=t(this._element).find(C.ACTIVE_ITEM)[0];var i=this._getItemIndex(this._activeElement);if(!(e>this._items.length-1||e<0))if(this._isSliding)t(this._element).one(p.SLID,function(){return n.to(e)});else{if(i===e)return this.pause(),void this.cycle();var s=e>i?h:u;this._slide(s,this._items[e])}},S.dispose=function(){t(this._element).off(i),t.removeData(this._element,n),this._items=null,this._config=null,this._element=null,this._interval=null,this._isPaused=null,this._isSliding=null,this._activeElement=null,this._indicatorsElement=null},S._getConfig=function(t){return t=r({},l,t),o.typeCheckConfig(e,t,c),t},S._addEventListeners=function(){var e=this;this._config.keyboard&&t(this._element).on(p.KEYDOWN,function(t){return e._keydown(t)}),"hover"===this._config.pause&&(t(this._element).on(p.MOUSEENTER,function(t){return e.pause(t)}).on(p.MOUSELEAVE,function(t){return e.cycle(t)}),"ontouchstart"in document.documentElement&&t(this._element).on(p.TOUCHEND,function(){e.pause(),e.touchTimeout&&clearTimeout(e.touchTimeout),e.touchTimeout=setTimeout(function(t){return e.cycle(t)},500+e._config.interval)}))},S._keydown=function(t){if(!/input|textarea/i.test(t.target.tagName))switch(t.which){case 37:t.preventDefault(),this.prev();break;case 39:t.preventDefault(),this.next()}},S._getItemIndex=function(e){return this._items=t.makeArray(t(e).parent().find(C.ITEM)),this._items.indexOf(e)},S._getItemByDirection=function(t,e){var n=t===h,i=t===u,s=this._getItemIndex(e),r=this._items.length-1;if((i&&0===s||n&&s===r)&&!this._config.wrap)return e;var o=(s+(t===u?-1:1))%this._items.length;return-1===o?this._items[this._items.length-1]:this._items[o]},S._triggerSlideEvent=function(e,n){var i=this._getItemIndex(e),s=this._getItemIndex(t(this._element).find(C.ACTIVE_ITEM)[0]),r=t.Event(p.SLIDE,{relatedTarget:e,direction:n,from:s,to:i});return t(this._element).trigger(r),r},S._setActiveIndicatorElement=function(e){if(this._indicatorsElement){t(this._indicatorsElement).find(C.ACTIVE).removeClass(m);var n=this._indicatorsElement.children[this._getItemIndex(e)];n&&t(n).addClass(m)}},S._slide=function(e,n){var i,s,r,a=this,l=t(this._element).find(C.ACTIVE_ITEM)[0],c=this._getItemIndex(l),u=n||l&&this._getItemByDirection(e,l),g=this._getItemIndex(u),S=Boolean(this._interval);if(e===h?(i=y,s=_,r=d):(i=b,s=w,r=f),u&&t(u).hasClass(m))this._isSliding=!1;else if(!this._triggerSlideEvent(u,r).isDefaultPrevented()&&l&&u){this._isSliding=!0,S&&this.pause(),this._setActiveIndicatorElement(u);var x=t.Event(p.SLID,{relatedTarget:u,direction:r,from:c,to:g});o.supportsTransitionEnd()&&t(this._element).hasClass(v)?(t(u).addClass(s),o.reflow(u),t(l).addClass(i),t(u).addClass(i),t(l).one(o.TRANSITION_END,function(){t(u).removeClass(i+" "+s).addClass(m),t(l).removeClass(m+" "+s+" "+i),a._isSliding=!1,setTimeout(function(){return t(a._element).trigger(x)},0)}).emulateTransitionEnd(600)):(t(l).removeClass(m),t(u).addClass(m),this._isSliding=!1,t(this._element).trigger(x)),S&&this.cycle()}},a._jQueryInterface=function(e){return this.each(function(){var i=t(this).data(n),s=r({},l,t(this).data());"object"==typeof e&&(s=r({},s,e));var o="string"==typeof e?e:s.slide;if(i||(i=new a(this,s),t(this).data(n,i)),"number"==typeof e)i.to(e);else if("string"==typeof o){if(void 0===i[o])throw new TypeError('No method named "'+o+'"');i[o]()}else s.interval&&(i.pause(),i.cycle())})},a._dataApiClickHandler=function(e){var i=o.getSelectorFromElement(this);if(i){var s=t(i)[0];if(s&&t(s).hasClass(g)){var l=r({},t(s).data(),t(this).data()),c=this.getAttribute("data-slide-to");c&&(l.interval=!1),a._jQueryInterface.call(t(s),l),c&&t(s).data(n).to(c),e.preventDefault()}}},s(a,null,[{key:"VERSION",get:function(){return"4.0.0"}},{key:"Default",get:function(){return l}}]),a}();return t(document).on(p.CLICK_DATA_API,C.DATA_SLIDE,S._dataApiClickHandler),t(window).on(p.LOAD_DATA_API,function(){t(C.DATA_RIDE).each(function(){var e=t(this);S._jQueryInterface.call(e,e.data())})}),t.fn[e]=S._jQueryInterface,t.fn[e].Constructor=S,t.fn[e].noConflict=function(){return t.fn[e]=a,S._jQueryInterface},S}(e),h=function(t){var e="collapse",n="bs.collapse",i=t.fn[e],a={toggle:!0,parent:""},l={toggle:"boolean",parent:"(string|element)"},c={SHOW:"show.bs.collapse",SHOWN:"shown.bs.collapse",HIDE:"hide.bs.collapse",HIDDEN:"hidden.bs.collapse",CLICK_DATA_API:"click.bs.collapse.data-api"},h="show",u="collapse",d="collapsing",f="collapsed",p="width",g="height",m={ACTIVES:".show, .collapsing",DATA_TOGGLE:'[data-toggle="collapse"]'},v=function(){function i(e,n){this._isTransitioning=!1,this._element=e,this._config=this._getConfig(n),this._triggerArray=t.makeArray(t('[data-toggle="collapse"][href="#'+e.id+'"],[data-toggle="collapse"][data-target="#'+e.id+'"]'));for(var i=t(m.DATA_TOGGLE),s=0;s<i.length;s++){var r=i[s],a=o.getSelectorFromElement(r);null!==a&&t(a).filter(e).length>0&&(this._selector=a,this._triggerArray.push(r))}this._parent=this._config.parent?this._getParent():null,this._config.parent||this._addAriaAndCollapsedClass(this._element,this._triggerArray),this._config.toggle&&this.toggle()}var v=i.prototype;return v.toggle=function(){t(this._element).hasClass(h)?this.hide():this.show()},v.show=function(){var e,s,r=this;if(!this._isTransitioning&&!t(this._element).hasClass(h)&&(this._parent&&0===(e=t.makeArray(t(this._parent).find(m.ACTIVES).filter('[data-parent="'+this._config.parent+'"]'))).length&&(e=null),!(e&&(s=t(e).not(this._selector).data(n))&&s._isTransitioning))){var a=t.Event(c.SHOW);if(t(this._element).trigger(a),!a.isDefaultPrevented()){e&&(i._jQueryInterface.call(t(e).not(this._selector),"hide"),s||t(e).data(n,null));var l=this._getDimension();t(this._element).removeClass(u).addClass(d),this._element.style[l]=0,this._triggerArray.length>0&&t(this._triggerArray).removeClass(f).attr("aria-expanded",!0),this.setTransitioning(!0);var p=function(){t(r._element).removeClass(d).addClass(u).addClass(h),r._element.style[l]="",r.setTransitioning(!1),t(r._element).trigger(c.SHOWN)};if(o.supportsTransitionEnd()){var g="scroll"+(l[0].toUpperCase()+l.slice(1));t(this._element).one(o.TRANSITION_END,p).emulateTransitionEnd(600),this._element.style[l]=this._element[g]+"px"}else p()}}},v.hide=function(){var e=this;if(!this._isTransitioning&&t(this._element).hasClass(h)){var n=t.Event(c.HIDE);if(t(this._element).trigger(n),!n.isDefaultPrevented()){var i=this._getDimension();if(this._element.style[i]=this._element.getBoundingClientRect()[i]+"px",o.reflow(this._element),t(this._element).addClass(d).removeClass(u).removeClass(h),this._triggerArray.length>0)for(var s=0;s<this._triggerArray.length;s++){var r=this._triggerArray[s],a=o.getSelectorFromElement(r);if(null!==a)t(a).hasClass(h)||t(r).addClass(f).attr("aria-expanded",!1)}this.setTransitioning(!0);var l=function(){e.setTransitioning(!1),t(e._element).removeClass(d).addClass(u).trigger(c.HIDDEN)};this._element.style[i]="",o.supportsTransitionEnd()?t(this._element).one(o.TRANSITION_END,l).emulateTransitionEnd(600):l()}}},v.setTransitioning=function(t){this._isTransitioning=t},v.dispose=function(){t.removeData(this._element,n),this._config=null,this._parent=null,this._element=null,this._triggerArray=null,this._isTransitioning=null},v._getConfig=function(t){return(t=r({},a,t)).toggle=Boolean(t.toggle),o.typeCheckConfig(e,t,l),t},v._getDimension=function(){return t(this._element).hasClass(p)?p:g},v._getParent=function(){var e=this,n=null;o.isElement(this._config.parent)?(n=this._config.parent,void 0!==this._config.parent.jquery&&(n=this._config.parent[0])):n=t(this._config.parent)[0];var s='[data-toggle="collapse"][data-parent="'+this._config.parent+'"]';return t(n).find(s).each(function(t,n){e._addAriaAndCollapsedClass(i._getTargetFromElement(n),[n])}),n},v._addAriaAndCollapsedClass=function(e,n){if(e){var i=t(e).hasClass(h);n.length>0&&t(n).toggleClass(f,!i).attr("aria-expanded",i)}},i._getTargetFromElement=function(e){var n=o.getSelectorFromElement(e);return n?t(n)[0]:null},i._jQueryInterface=function(e){return this.each(function(){var s=t(this),o=s.data(n),l=r({},a,s.data(),"object"==typeof e&&e);if(!o&&l.toggle&&/show|hide/.test(e)&&(l.toggle=!1),o||(o=new i(this,l),s.data(n,o)),"string"==typeof e){if(void 0===o[e])throw new TypeError('No method named "'+e+'"');o[e]()}})},s(i,null,[{key:"VERSION",get:function(){return"4.0.0"}},{key:"Default",get:function(){return a}}]),i}();return t(document).on(c.CLICK_DATA_API,m.DATA_TOGGLE,function(e){"A"===e.currentTarget.tagName&&e.preventDefault();var i=t(this),s=o.getSelectorFromElement(this);t(s).each(function(){var e=t(this),s=e.data(n)?"toggle":i.data();v._jQueryInterface.call(e,s)})}),t.fn[e]=v._jQueryInterface,t.fn[e].Constructor=v,t.fn[e].noConflict=function(){return t.fn[e]=i,v._jQueryInterface},v}(e),u=function(t){var e="dropdown",i="bs.dropdown",a="."+i,l=t.fn[e],c=new RegExp("38|40|27"),h={HIDE:"hide"+a,HIDDEN:"hidden"+a,SHOW:"show"+a,SHOWN:"shown"+a,CLICK:"click"+a,CLICK_DATA_API:"click.bs.dropdown.data-api",KEYDOWN_DATA_API:"keydown.bs.dropdown.data-api",KEYUP_DATA_API:"keyup.bs.dropdown.data-api"},u="disabled",d="show",f="dropup",p="dropright",g="dropleft",m="dropdown-menu-right",v="dropdown-menu-left",b="position-static",y='[data-toggle="dropdown"]',_=".dropdown form",w=".dropdown-menu",C=".navbar-nav",S=".dropdown-menu .dropdown-item:not(.disabled)",x="top-start",T="top-end",D="bottom-start",E="bottom-end",I="right-start",A="left-start",k={offset:0,flip:!0,boundary:"scrollParent"},O={offset:"(number|string|function)",flip:"boolean",boundary:"(string|element)"},N=function(){function l(t,e){this._element=t,this._popper=null,this._config=this._getConfig(e),this._menu=this._getMenuElement(),this._inNavbar=this._detectNavbar(),this._addEventListeners()}var _=l.prototype;return _.toggle=function(){if(!this._element.disabled&&!t(this._element).hasClass(u)){var e=l._getParentFromElement(this._element),i=t(this._menu).hasClass(d);if(l._clearMenus(),!i){var s={relatedTarget:this._element},r=t.Event(h.SHOW,s);if(t(e).trigger(r),!r.isDefaultPrevented()){if(!this._inNavbar){if(void 0===n)throw new TypeError("Bootstrap dropdown require Popper.js (https://popper.js.org)");var o=this._element;t(e).hasClass(f)&&(t(this._menu).hasClass(v)||t(this._menu).hasClass(m))&&(o=e),"scrollParent"!==this._config.boundary&&t(e).addClass(b),this._popper=new n(o,this._menu,this._getPopperConfig())}"ontouchstart"in document.documentElement&&0===t(e).closest(C).length&&t("body").children().on("mouseover",null,t.noop),this._element.focus(),this._element.setAttribute("aria-expanded",!0),t(this._menu).toggleClass(d),t(e).toggleClass(d).trigger(t.Event(h.SHOWN,s))}}}},_.dispose=function(){t.removeData(this._element,i),t(this._element).off(a),this._element=null,this._menu=null,null!==this._popper&&(this._popper.destroy(),this._popper=null)},_.update=function(){this._inNavbar=this._detectNavbar(),null!==this._popper&&this._popper.scheduleUpdate()},_._addEventListeners=function(){var e=this;t(this._element).on(h.CLICK,function(t){t.preventDefault(),t.stopPropagation(),e.toggle()})},_._getConfig=function(n){return n=r({},this.constructor.Default,t(this._element).data(),n),o.typeCheckConfig(e,n,this.constructor.DefaultType),n},_._getMenuElement=function(){if(!this._menu){var e=l._getParentFromElement(this._element);this._menu=t(e).find(w)[0]}return this._menu},_._getPlacement=function(){var e=t(this._element).parent(),n=D;return e.hasClass(f)?(n=x,t(this._menu).hasClass(m)&&(n=T)):e.hasClass(p)?n=I:e.hasClass(g)?n=A:t(this._menu).hasClass(m)&&(n=E),n},_._detectNavbar=function(){return t(this._element).closest(".navbar").length>0},_._getPopperConfig=function(){var t=this,e={};return"function"==typeof this._config.offset?e.fn=function(e){return e.offsets=r({},e.offsets,t._config.offset(e.offsets)||{}),e}:e.offset=this._config.offset,{placement:this._getPlacement(),modifiers:{offset:e,flip:{enabled:this._config.flip},preventOverflow:{boundariesElement:this._config.boundary}}}},l._jQueryInterface=function(e){return this.each(function(){var n=t(this).data(i);if(n||(n=new l(this,"object"==typeof e?e:null),t(this).data(i,n)),"string"==typeof e){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e]()}})},l._clearMenus=function(e){if(!e||3!==e.which&&("keyup"!==e.type||9===e.which))for(var n=t.makeArray(t(y)),s=0;s<n.length;s++){var r=l._getParentFromElement(n[s]),o=t(n[s]).data(i),a={relatedTarget:n[s]};if(o){var c=o._menu;if(t(r).hasClass(d)&&!(e&&("click"===e.type&&/input|textarea/i.test(e.target.tagName)||"keyup"===e.type&&9===e.which)&&t.contains(r,e.target))){var u=t.Event(h.HIDE,a);t(r).trigger(u),u.isDefaultPrevented()||("ontouchstart"in document.documentElement&&t("body").children().off("mouseover",null,t.noop),n[s].setAttribute("aria-expanded","false"),t(c).removeClass(d),t(r).removeClass(d).trigger(t.Event(h.HIDDEN,a)))}}}},l._getParentFromElement=function(e){var n,i=o.getSelectorFromElement(e);return i&&(n=t(i)[0]),n||e.parentNode},l._dataApiKeydownHandler=function(e){if((/input|textarea/i.test(e.target.tagName)?!(32===e.which||27!==e.which&&(40!==e.which&&38!==e.which||t(e.target).closest(w).length)):c.test(e.which))&&(e.preventDefault(),e.stopPropagation(),!this.disabled&&!t(this).hasClass(u))){var n=l._getParentFromElement(this),i=t(n).hasClass(d);if((i||27===e.which&&32===e.which)&&(!i||27!==e.which&&32!==e.which)){var s=t(n).find(S).get();if(0!==s.length){var r=s.indexOf(e.target);38===e.which&&r>0&&r--,40===e.which&&r<s.length-1&&r++,r<0&&(r=0),s[r].focus()}}else{if(27===e.which){var o=t(n).find(y)[0];t(o).trigger("focus")}t(this).trigger("click")}}},s(l,null,[{key:"VERSION",get:function(){return"4.0.0"}},{key:"Default",get:function(){return k}},{key:"DefaultType",get:function(){return O}}]),l}();return t(document).on(h.KEYDOWN_DATA_API,y,N._dataApiKeydownHandler).on(h.KEYDOWN_DATA_API,w,N._dataApiKeydownHandler).on(h.CLICK_DATA_API+" "+h.KEYUP_DATA_API,N._clearMenus).on(h.CLICK_DATA_API,y,function(e){e.preventDefault(),e.stopPropagation(),N._jQueryInterface.call(t(this),"toggle")}).on(h.CLICK_DATA_API,_,function(t){t.stopPropagation()}),t.fn[e]=N._jQueryInterface,t.fn[e].Constructor=N,t.fn[e].noConflict=function(){return t.fn[e]=l,N._jQueryInterface},N}(e),d=function(t){var e="modal",n=".bs.modal",i=t.fn.modal,a={backdrop:!0,keyboard:!0,focus:!0,show:!0},l={backdrop:"(boolean|string)",keyboard:"boolean",focus:"boolean",show:"boolean"},c={HIDE:"hide.bs.modal",HIDDEN:"hidden.bs.modal",SHOW:"show.bs.modal",SHOWN:"shown.bs.modal",FOCUSIN:"focusin.bs.modal",RESIZE:"resize.bs.modal",CLICK_DISMISS:"click.dismiss.bs.modal",KEYDOWN_DISMISS:"keydown.dismiss.bs.modal",MOUSEUP_DISMISS:"mouseup.dismiss.bs.modal",MOUSEDOWN_DISMISS:"mousedown.dismiss.bs.modal",CLICK_DATA_API:"click.bs.modal.data-api"},h="modal-scrollbar-measure",u="modal-backdrop",d="modal-open",f="fade",p="show",g={DIALOG:".modal-dialog",DATA_TOGGLE:'[data-toggle="modal"]',DATA_DISMISS:'[data-dismiss="modal"]',FIXED_CONTENT:".fixed-top, .fixed-bottom, .is-fixed, .sticky-top",STICKY_CONTENT:".sticky-top",NAVBAR_TOGGLER:".navbar-toggler"},m=function(){function i(e,n){this._config=this._getConfig(n),this._element=e,this._dialog=t(e).find(g.DIALOG)[0],this._backdrop=null,this._isShown=!1,this._isBodyOverflowing=!1,this._ignoreBackdropClick=!1,this._originalBodyPadding=0,this._scrollbarWidth=0}var m=i.prototype;return m.toggle=function(t){return this._isShown?this.hide():this.show(t)},m.show=function(e){var n=this;if(!this._isTransitioning&&!this._isShown){o.supportsTransitionEnd()&&t(this._element).hasClass(f)&&(this._isTransitioning=!0);var i=t.Event(c.SHOW,{relatedTarget:e});t(this._element).trigger(i),this._isShown||i.isDefaultPrevented()||(this._isShown=!0,this._checkScrollbar(),this._setScrollbar(),this._adjustDialog(),t(document.body).addClass(d),this._setEscapeEvent(),this._setResizeEvent(),t(this._element).on(c.CLICK_DISMISS,g.DATA_DISMISS,function(t){return n.hide(t)}),t(this._dialog).on(c.MOUSEDOWN_DISMISS,function(){t(n._element).one(c.MOUSEUP_DISMISS,function(e){t(e.target).is(n._element)&&(n._ignoreBackdropClick=!0)})}),this._showBackdrop(function(){return n._showElement(e)}))}},m.hide=function(e){var n=this;if(e&&e.preventDefault(),!this._isTransitioning&&this._isShown){var i=t.Event(c.HIDE);if(t(this._element).trigger(i),this._isShown&&!i.isDefaultPrevented()){this._isShown=!1;var s=o.supportsTransitionEnd()&&t(this._element).hasClass(f);s&&(this._isTransitioning=!0),this._setEscapeEvent(),this._setResizeEvent(),t(document).off(c.FOCUSIN),t(this._element).removeClass(p),t(this._element).off(c.CLICK_DISMISS),t(this._dialog).off(c.MOUSEDOWN_DISMISS),s?t(this._element).one(o.TRANSITION_END,function(t){return n._hideModal(t)}).emulateTransitionEnd(300):this._hideModal()}}},m.dispose=function(){t.removeData(this._element,"bs.modal"),t(window,document,this._element,this._backdrop).off(n),this._config=null,this._element=null,this._dialog=null,this._backdrop=null,this._isShown=null,this._isBodyOverflowing=null,this._ignoreBackdropClick=null,this._scrollbarWidth=null},m.handleUpdate=function(){this._adjustDialog()},m._getConfig=function(t){return t=r({},a,t),o.typeCheckConfig(e,t,l),t},m._showElement=function(e){var n=this,i=o.supportsTransitionEnd()&&t(this._element).hasClass(f);this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE||document.body.appendChild(this._element),this._element.style.display="block",this._element.removeAttribute("aria-hidden"),this._element.scrollTop=0,i&&o.reflow(this._element),t(this._element).addClass(p),this._config.focus&&this._enforceFocus();var s=t.Event(c.SHOWN,{relatedTarget:e}),r=function(){n._config.focus&&n._element.focus(),n._isTransitioning=!1,t(n._element).trigger(s)};i?t(this._dialog).one(o.TRANSITION_END,r).emulateTransitionEnd(300):r()},m._enforceFocus=function(){var e=this;t(document).off(c.FOCUSIN).on(c.FOCUSIN,function(n){document!==n.target&&e._element!==n.target&&0===t(e._element).has(n.target).length&&e._element.focus()})},m._setEscapeEvent=function(){var e=this;this._isShown&&this._config.keyboard?t(this._element).on(c.KEYDOWN_DISMISS,function(t){27===t.which&&(t.preventDefault(),e.hide())}):this._isShown||t(this._element).off(c.KEYDOWN_DISMISS)},m._setResizeEvent=function(){var e=this;this._isShown?t(window).on(c.RESIZE,function(t){return e.handleUpdate(t)}):t(window).off(c.RESIZE)},m._hideModal=function(){var e=this;this._element.style.display="none",this._element.setAttribute("aria-hidden",!0),this._isTransitioning=!1,this._showBackdrop(function(){t(document.body).removeClass(d),e._resetAdjustments(),e._resetScrollbar(),t(e._element).trigger(c.HIDDEN)})},m._removeBackdrop=function(){this._backdrop&&(t(this._backdrop).remove(),this._backdrop=null)},m._showBackdrop=function(e){var n=this,i=t(this._element).hasClass(f)?f:"";if(this._isShown&&this._config.backdrop){var s=o.supportsTransitionEnd()&&i;if(this._backdrop=document.createElement("div"),this._backdrop.className=u,i&&t(this._backdrop).addClass(i),t(this._backdrop).appendTo(document.body),t(this._element).on(c.CLICK_DISMISS,function(t){n._ignoreBackdropClick?n._ignoreBackdropClick=!1:t.target===t.currentTarget&&("static"===n._config.backdrop?n._element.focus():n.hide())}),s&&o.reflow(this._backdrop),t(this._backdrop).addClass(p),!e)return;if(!s)return void e();t(this._backdrop).one(o.TRANSITION_END,e).emulateTransitionEnd(150)}else if(!this._isShown&&this._backdrop){t(this._backdrop).removeClass(p);var r=function(){n._removeBackdrop(),e&&e()};o.supportsTransitionEnd()&&t(this._element).hasClass(f)?t(this._backdrop).one(o.TRANSITION_END,r).emulateTransitionEnd(150):r()}else e&&e()},m._adjustDialog=function(){var t=this._element.scrollHeight>document.documentElement.clientHeight;!this._isBodyOverflowing&&t&&(this._element.style.paddingLeft=this._scrollbarWidth+"px"),this._isBodyOverflowing&&!t&&(this._element.style.paddingRight=this._scrollbarWidth+"px")},m._resetAdjustments=function(){this._element.style.paddingLeft="",this._element.style.paddingRight=""},m._checkScrollbar=function(){var t=document.body.getBoundingClientRect();this._isBodyOverflowing=t.left+t.right<window.innerWidth,this._scrollbarWidth=this._getScrollbarWidth()},m._setScrollbar=function(){var e=this;if(this._isBodyOverflowing){t(g.FIXED_CONTENT).each(function(n,i){var s=t(i)[0].style.paddingRight,r=t(i).css("padding-right");t(i).data("padding-right",s).css("padding-right",parseFloat(r)+e._scrollbarWidth+"px")}),t(g.STICKY_CONTENT).each(function(n,i){var s=t(i)[0].style.marginRight,r=t(i).css("margin-right");t(i).data("margin-right",s).css("margin-right",parseFloat(r)-e._scrollbarWidth+"px")}),t(g.NAVBAR_TOGGLER).each(function(n,i){var s=t(i)[0].style.marginRight,r=t(i).css("margin-right");t(i).data("margin-right",s).css("margin-right",parseFloat(r)+e._scrollbarWidth+"px")});var n=document.body.style.paddingRight,i=t("body").css("padding-right");t("body").data("padding-right",n).css("padding-right",parseFloat(i)+this._scrollbarWidth+"px")}},m._resetScrollbar=function(){t(g.FIXED_CONTENT).each(function(e,n){var i=t(n).data("padding-right");void 0!==i&&t(n).css("padding-right",i).removeData("padding-right")}),t(g.STICKY_CONTENT+", "+g.NAVBAR_TOGGLER).each(function(e,n){var i=t(n).data("margin-right");void 0!==i&&t(n).css("margin-right",i).removeData("margin-right")});var e=t("body").data("padding-right");void 0!==e&&t("body").css("padding-right",e).removeData("padding-right")},m._getScrollbarWidth=function(){var t=document.createElement("div");t.className=h,document.body.appendChild(t);var e=t.getBoundingClientRect().width-t.clientWidth;return document.body.removeChild(t),e},i._jQueryInterface=function(e,n){return this.each(function(){var s=t(this).data("bs.modal"),o=r({},i.Default,t(this).data(),"object"==typeof e&&e);if(s||(s=new i(this,o),t(this).data("bs.modal",s)),"string"==typeof e){if(void 0===s[e])throw new TypeError('No method named "'+e+'"');s[e](n)}else o.show&&s.show(n)})},s(i,null,[{key:"VERSION",get:function(){return"4.0.0"}},{key:"Default",get:function(){return a}}]),i}();return t(document).on(c.CLICK_DATA_API,g.DATA_TOGGLE,function(e){var n,i=this,s=o.getSelectorFromElement(this);s&&(n=t(s)[0]);var a=t(n).data("bs.modal")?"toggle":r({},t(n).data(),t(this).data());"A"!==this.tagName&&"AREA"!==this.tagName||e.preventDefault();var l=t(n).one(c.SHOW,function(e){e.isDefaultPrevented()||l.one(c.HIDDEN,function(){t(i).is(":visible")&&i.focus()})});m._jQueryInterface.call(t(n),a,this)}),t.fn.modal=m._jQueryInterface,t.fn.modal.Constructor=m,t.fn.modal.noConflict=function(){return t.fn.modal=i,m._jQueryInterface},m}(e),f=function(t){var e="tooltip",i=".bs.tooltip",a=t.fn[e],l=new RegExp("(^|\\s)bs-tooltip\\S+","g"),c={animation:"boolean",template:"string",title:"(string|element|function)",trigger:"string",delay:"(number|object)",html:"boolean",selector:"(string|boolean)",placement:"(string|function)",offset:"(number|string)",container:"(string|element|boolean)",fallbackPlacement:"(string|array)",boundary:"(string|element)"},h={AUTO:"auto",TOP:"top",RIGHT:"right",BOTTOM:"bottom",LEFT:"left"},u={animation:!0,template:'<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,selector:!1,placement:"top",offset:0,container:!1,fallbackPlacement:"flip",boundary:"scrollParent"},d="show",f="out",p={HIDE:"hide"+i,HIDDEN:"hidden"+i,SHOW:"show"+i,SHOWN:"shown"+i,INSERTED:"inserted"+i,CLICK:"click"+i,FOCUSIN:"focusin"+i,FOCUSOUT:"focusout"+i,MOUSEENTER:"mouseenter"+i,MOUSELEAVE:"mouseleave"+i},g="fade",m="show",v=".tooltip-inner",b=".arrow",y="hover",_="focus",w="click",C="manual",S=function(){function a(t,e){if(void 0===n)throw new TypeError("Bootstrap tooltips require Popper.js (https://popper.js.org)");this._isEnabled=!0,this._timeout=0,this._hoverState="",this._activeTrigger={},this._popper=null,this.element=t,this.config=this._getConfig(e),this.tip=null,this._setListeners()}var S=a.prototype;return S.enable=function(){this._isEnabled=!0},S.disable=function(){this._isEnabled=!1},S.toggleEnabled=function(){this._isEnabled=!this._isEnabled},S.toggle=function(e){if(this._isEnabled)if(e){var n=this.constructor.DATA_KEY,i=t(e.currentTarget).data(n);i||(i=new this.constructor(e.currentTarget,this._getDelegateConfig()),t(e.currentTarget).data(n,i)),i._activeTrigger.click=!i._activeTrigger.click,i._isWithActiveTrigger()?i._enter(null,i):i._leave(null,i)}else{if(t(this.getTipElement()).hasClass(m))return void this._leave(null,this);this._enter(null,this)}},S.dispose=function(){clearTimeout(this._timeout),t.removeData(this.element,this.constructor.DATA_KEY),t(this.element).off(this.constructor.EVENT_KEY),t(this.element).closest(".modal").off("hide.bs.modal"),this.tip&&t(this.tip).remove(),this._isEnabled=null,this._timeout=null,this._hoverState=null,this._activeTrigger=null,null!==this._popper&&this._popper.destroy(),this._popper=null,this.element=null,this.config=null,this.tip=null},S.show=function(){var e=this;if("none"===t(this.element).css("display"))throw new Error("Please use show on visible elements");var i=t.Event(this.constructor.Event.SHOW);if(this.isWithContent()&&this._isEnabled){t(this.element).trigger(i);var s=t.contains(this.element.ownerDocument.documentElement,this.element);if(i.isDefaultPrevented()||!s)return;var r=this.getTipElement(),l=o.getUID(this.constructor.NAME);r.setAttribute("id",l),this.element.setAttribute("aria-describedby",l),this.setContent(),this.config.animation&&t(r).addClass(g);var c="function"==typeof this.config.placement?this.config.placement.call(this,r,this.element):this.config.placement,h=this._getAttachment(c);this.addAttachmentClass(h);var u=!1===this.config.container?document.body:t(this.config.container);t(r).data(this.constructor.DATA_KEY,this),t.contains(this.element.ownerDocument.documentElement,this.tip)||t(r).appendTo(u),t(this.element).trigger(this.constructor.Event.INSERTED),this._popper=new n(this.element,r,{placement:h,modifiers:{offset:{offset:this.config.offset},flip:{behavior:this.config.fallbackPlacement},arrow:{element:b},preventOverflow:{boundariesElement:this.config.boundary}},onCreate:function(t){t.originalPlacement!==t.placement&&e._handlePopperPlacementChange(t)},onUpdate:function(t){e._handlePopperPlacementChange(t)}}),t(r).addClass(m),"ontouchstart"in document.documentElement&&t("body").children().on("mouseover",null,t.noop);var d=function(){e.config.animation&&e._fixTransition();var n=e._hoverState;e._hoverState=null,t(e.element).trigger(e.constructor.Event.SHOWN),n===f&&e._leave(null,e)};o.supportsTransitionEnd()&&t(this.tip).hasClass(g)?t(this.tip).one(o.TRANSITION_END,d).emulateTransitionEnd(a._TRANSITION_DURATION):d()}},S.hide=function(e){var n=this,i=this.getTipElement(),s=t.Event(this.constructor.Event.HIDE),r=function(){n._hoverState!==d&&i.parentNode&&i.parentNode.removeChild(i),n._cleanTipClass(),n.element.removeAttribute("aria-describedby"),t(n.element).trigger(n.constructor.Event.HIDDEN),null!==n._popper&&n._popper.destroy(),e&&e()};t(this.element).trigger(s),s.isDefaultPrevented()||(t(i).removeClass(m),"ontouchstart"in document.documentElement&&t("body").children().off("mouseover",null,t.noop),this._activeTrigger[w]=!1,this._activeTrigger[_]=!1,this._activeTrigger[y]=!1,o.supportsTransitionEnd()&&t(this.tip).hasClass(g)?t(i).one(o.TRANSITION_END,r).emulateTransitionEnd(150):r(),this._hoverState="")},S.update=function(){null!==this._popper&&this._popper.scheduleUpdate()},S.isWithContent=function(){return Boolean(this.getTitle())},S.addAttachmentClass=function(e){t(this.getTipElement()).addClass("bs-tooltip-"+e)},S.getTipElement=function(){return this.tip=this.tip||t(this.config.template)[0],this.tip},S.setContent=function(){var e=t(this.getTipElement());this.setElementContent(e.find(v),this.getTitle()),e.removeClass(g+" "+m)},S.setElementContent=function(e,n){var i=this.config.html;"object"==typeof n&&(n.nodeType||n.jquery)?i?t(n).parent().is(e)||e.empty().append(n):e.text(t(n).text()):e[i?"html":"text"](n)},S.getTitle=function(){var t=this.element.getAttribute("data-original-title");return t||(t="function"==typeof this.config.title?this.config.title.call(this.element):this.config.title),t},S._getAttachment=function(t){return h[t.toUpperCase()]},S._setListeners=function(){var e=this;this.config.trigger.split(" ").forEach(function(n){if("click"===n)t(e.element).on(e.constructor.Event.CLICK,e.config.selector,function(t){return e.toggle(t)});else if(n!==C){var i=n===y?e.constructor.Event.MOUSEENTER:e.constructor.Event.FOCUSIN,s=n===y?e.constructor.Event.MOUSELEAVE:e.constructor.Event.FOCUSOUT;t(e.element).on(i,e.config.selector,function(t){return e._enter(t)}).on(s,e.config.selector,function(t){return e._leave(t)})}t(e.element).closest(".modal").on("hide.bs.modal",function(){return e.hide()})}),this.config.selector?this.config=r({},this.config,{trigger:"manual",selector:""}):this._fixTitle()},S._fixTitle=function(){var t=typeof this.element.getAttribute("data-original-title");(this.element.getAttribute("title")||"string"!==t)&&(this.element.setAttribute("data-original-title",this.element.getAttribute("title")||""),this.element.setAttribute("title",""))},S._enter=function(e,n){var i=this.constructor.DATA_KEY;(n=n||t(e.currentTarget).data(i))||(n=new this.constructor(e.currentTarget,this._getDelegateConfig()),t(e.currentTarget).data(i,n)),e&&(n._activeTrigger["focusin"===e.type?_:y]=!0),t(n.getTipElement()).hasClass(m)||n._hoverState===d?n._hoverState=d:(clearTimeout(n._timeout),n._hoverState=d,n.config.delay&&n.config.delay.show?n._timeout=setTimeout(function(){n._hoverState===d&&n.show()},n.config.delay.show):n.show())},S._leave=function(e,n){var i=this.constructor.DATA_KEY;(n=n||t(e.currentTarget).data(i))||(n=new this.constructor(e.currentTarget,this._getDelegateConfig()),t(e.currentTarget).data(i,n)),e&&(n._activeTrigger["focusout"===e.type?_:y]=!1),n._isWithActiveTrigger()||(clearTimeout(n._timeout),n._hoverState=f,n.config.delay&&n.config.delay.hide?n._timeout=setTimeout(function(){n._hoverState===f&&n.hide()},n.config.delay.hide):n.hide())},S._isWithActiveTrigger=function(){for(var t in this._activeTrigger)if(this._activeTrigger[t])return!0;return!1},S._getConfig=function(n){return"number"==typeof(n=r({},this.constructor.Default,t(this.element).data(),n)).delay&&(n.delay={show:n.delay,hide:n.delay}),"number"==typeof n.title&&(n.title=n.title.toString()),"number"==typeof n.content&&(n.content=n.content.toString()),o.typeCheckConfig(e,n,this.constructor.DefaultType),n},S._getDelegateConfig=function(){var t={};if(this.config)for(var e in this.config)this.constructor.Default[e]!==this.config[e]&&(t[e]=this.config[e]);return t},S._cleanTipClass=function(){var e=t(this.getTipElement()),n=e.attr("class").match(l);null!==n&&n.length>0&&e.removeClass(n.join(""))},S._handlePopperPlacementChange=function(t){this._cleanTipClass(),this.addAttachmentClass(this._getAttachment(t.placement))},S._fixTransition=function(){var e=this.getTipElement(),n=this.config.animation;null===e.getAttribute("x-placement")&&(t(e).removeClass(g),this.config.animation=!1,this.hide(),this.show(),this.config.animation=n)},a._jQueryInterface=function(e){return this.each(function(){var n=t(this).data("bs.tooltip"),i="object"==typeof e&&e;if((n||!/dispose|hide/.test(e))&&(n||(n=new a(this,i),t(this).data("bs.tooltip",n)),"string"==typeof e)){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e]()}})},s(a,null,[{key:"VERSION",get:function(){return"4.0.0"}},{key:"Default",get:function(){return u}},{key:"NAME",get:function(){return e}},{key:"DATA_KEY",get:function(){return"bs.tooltip"}},{key:"Event",get:function(){return p}},{key:"EVENT_KEY",get:function(){return i}},{key:"DefaultType",get:function(){return c}}]),a}();return t.fn[e]=S._jQueryInterface,t.fn[e].Constructor=S,t.fn[e].noConflict=function(){return t.fn[e]=a,S._jQueryInterface},S}(e),p=function(t){var e="popover",n=".bs.popover",i=t.fn[e],o=new RegExp("(^|\\s)bs-popover\\S+","g"),a=r({},f.Default,{placement:"right",trigger:"click",content:"",template:'<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>'}),l=r({},f.DefaultType,{content:"(string|element|function)"}),c="fade",h="show",u=".popover-header",d=".popover-body",p={HIDE:"hide"+n,HIDDEN:"hidden"+n,SHOW:"show"+n,SHOWN:"shown"+n,INSERTED:"inserted"+n,CLICK:"click"+n,FOCUSIN:"focusin"+n,FOCUSOUT:"focusout"+n,MOUSEENTER:"mouseenter"+n,MOUSELEAVE:"mouseleave"+n},g=function(i){var r,f;function g(){return i.apply(this,arguments)||this}f=i,(r=g).prototype=Object.create(f.prototype),r.prototype.constructor=r,r.__proto__=f;var m=g.prototype;return m.isWithContent=function(){return this.getTitle()||this._getContent()},m.addAttachmentClass=function(e){t(this.getTipElement()).addClass("bs-popover-"+e)},m.getTipElement=function(){return this.tip=this.tip||t(this.config.template)[0],this.tip},m.setContent=function(){var e=t(this.getTipElement());this.setElementContent(e.find(u),this.getTitle());var n=this._getContent();"function"==typeof n&&(n=n.call(this.element)),this.setElementContent(e.find(d),n),e.removeClass(c+" "+h)},m._getContent=function(){return this.element.getAttribute("data-content")||this.config.content},m._cleanTipClass=function(){var e=t(this.getTipElement()),n=e.attr("class").match(o);null!==n&&n.length>0&&e.removeClass(n.join(""))},g._jQueryInterface=function(e){return this.each(function(){var n=t(this).data("bs.popover"),i="object"==typeof e?e:null;if((n||!/destroy|hide/.test(e))&&(n||(n=new g(this,i),t(this).data("bs.popover",n)),"string"==typeof e)){if(void 0===n[e])throw new TypeError('No method named "'+e+'"');n[e]()}})},s(g,null,[{key:"VERSION",get:function(){return"4.0.0"}},{key:"Default",get:function(){return a}},{key:"NAME",get:function(){return e}},{key:"DATA_KEY",get:function(){return"bs.popover"}},{key:"Event",get:function(){return p}},{key:"EVENT_KEY",get:function(){return n}},{key:"DefaultType",get:function(){return l}}]),g}(f);return t.fn[e]=g._jQueryInterface,t.fn[e].Constructor=g,t.fn[e].noConflict=function(){return t.fn[e]=i,g._jQueryInterface},g}(e),g=function(t){var e="scrollspy",n=t.fn[e],i={offset:10,method:"auto",target:""},a={offset:"number",method:"string",target:"(string|element)"},l={ACTIVATE:"activate.bs.scrollspy",SCROLL:"scroll.bs.scrollspy",LOAD_DATA_API:"load.bs.scrollspy.data-api"},c="dropdown-item",h="active",u={DATA_SPY:'[data-spy="scroll"]',ACTIVE:".active",NAV_LIST_GROUP:".nav, .list-group",NAV_LINKS:".nav-link",NAV_ITEMS:".nav-item",LIST_ITEMS:".list-group-item",DROPDOWN:".dropdown",DROPDOWN_ITEMS:".dropdown-item",DROPDOWN_TOGGLE:".dropdown-toggle"},d="offset",f="position",p=function(){function n(e,n){var i=this;this._element=e,this._scrollElement="BODY"===e.tagName?window:e,this._config=this._getConfig(n),this._selector=this._config.target+" "+u.NAV_LINKS+","+this._config.target+" "+u.LIST_ITEMS+","+this._config.target+" "+u.DROPDOWN_ITEMS,this._offsets=[],this._targets=[],this._activeTarget=null,this._scrollHeight=0,t(this._scrollElement).on(l.SCROLL,function(t){return i._process(t)}),this.refresh(),this._process()}var p=n.prototype;return p.refresh=function(){var e=this,n=this._scrollElement===this._scrollElement.window?d:f,i="auto"===this._config.method?n:this._config.method,s=i===f?this._getScrollTop():0;this._offsets=[],this._targets=[],this._scrollHeight=this._getScrollHeight(),t.makeArray(t(this._selector)).map(function(e){var n,r=o.getSelectorFromElement(e);if(r&&(n=t(r)[0]),n){var a=n.getBoundingClientRect();if(a.width||a.height)return[t(n)[i]().top+s,r]}return null}).filter(function(t){return t}).sort(function(t,e){return t[0]-e[0]}).forEach(function(t){e._offsets.push(t[0]),e._targets.push(t[1])})},p.dispose=function(){t.removeData(this._element,"bs.scrollspy"),t(this._scrollElement).off(".bs.scrollspy"),this._element=null,this._scrollElement=null,this._config=null,this._selector=null,this._offsets=null,this._targets=null,this._activeTarget=null,this._scrollHeight=null},p._getConfig=function(n){if("string"!=typeof(n=r({},i,n)).target){var s=t(n.target).attr("id");s||(s=o.getUID(e),t(n.target).attr("id",s)),n.target="#"+s}return o.typeCheckConfig(e,n,a),n},p._getScrollTop=function(){return this._scrollElement===window?this._scrollElement.pageYOffset:this._scrollElement.scrollTop},p._getScrollHeight=function(){return this._scrollElement.scrollHeight||Math.max(document.body.scrollHeight,document.documentElement.scrollHeight)},p._getOffsetHeight=function(){return this._scrollElement===window?window.innerHeight:this._scrollElement.getBoundingClientRect().height},p._process=function(){var t=this._getScrollTop()+this._config.offset,e=this._getScrollHeight(),n=this._config.offset+e-this._getOffsetHeight();if(this._scrollHeight!==e&&this.refresh(),t>=n){var i=this._targets[this._targets.length-1];this._activeTarget!==i&&this._activate(i)}else{if(this._activeTarget&&t<this._offsets[0]&&this._offsets[0]>0)return this._activeTarget=null,void this._clear();for(var s=this._offsets.length;s--;){this._activeTarget!==this._targets[s]&&t>=this._offsets[s]&&(void 0===this._offsets[s+1]||t<this._offsets[s+1])&&this._activate(this._targets[s])}}},p._activate=function(e){this._activeTarget=e,this._clear();var n=this._selector.split(",");n=n.map(function(t){return t+'[data-target="'+e+'"],'+t+'[href="'+e+'"]'});var i=t(n.join(","));i.hasClass(c)?(i.closest(u.DROPDOWN).find(u.DROPDOWN_TOGGLE).addClass(h),i.addClass(h)):(i.addClass(h),i.parents(u.NAV_LIST_GROUP).prev(u.NAV_LINKS+", "+u.LIST_ITEMS).addClass(h),i.parents(u.NAV_LIST_GROUP).prev(u.NAV_ITEMS).children(u.NAV_LINKS).addClass(h)),t(this._scrollElement).trigger(l.ACTIVATE,{relatedTarget:e})},p._clear=function(){t(this._selector).filter(u.ACTIVE).removeClass(h)},n._jQueryInterface=function(e){return this.each(function(){var i=t(this).data("bs.scrollspy");if(i||(i=new n(this,"object"==typeof e&&e),t(this).data("bs.scrollspy",i)),"string"==typeof e){if(void 0===i[e])throw new TypeError('No method named "'+e+'"');i[e]()}})},s(n,null,[{key:"VERSION",get:function(){return"4.0.0"}},{key:"Default",get:function(){return i}}]),n}();return t(window).on(l.LOAD_DATA_API,function(){for(var e=t.makeArray(t(u.DATA_SPY)),n=e.length;n--;){var i=t(e[n]);p._jQueryInterface.call(i,i.data())}}),t.fn[e]=p._jQueryInterface,t.fn[e].Constructor=p,t.fn[e].noConflict=function(){return t.fn[e]=n,p._jQueryInterface},p}(e),m=function(t){var e=t.fn.tab,n={HIDE:"hide.bs.tab",HIDDEN:"hidden.bs.tab",SHOW:"show.bs.tab",SHOWN:"shown.bs.tab",CLICK_DATA_API:"click.bs.tab.data-api"},i="dropdown-menu",r="active",a="disabled",l="fade",c="show",h=".dropdown",u=".nav, .list-group",d=".active",f="> li > .active",p='[data-toggle="tab"], [data-toggle="pill"], [data-toggle="list"]',g=".dropdown-toggle",m="> .dropdown-menu .active",v=function(){function e(t){this._element=t}var p=e.prototype;return p.show=function(){var e=this;if(!(this._element.parentNode&&this._element.parentNode.nodeType===Node.ELEMENT_NODE&&t(this._element).hasClass(r)||t(this._element).hasClass(a))){var i,s,l=t(this._element).closest(u)[0],c=o.getSelectorFromElement(this._element);if(l){var h="UL"===l.nodeName?f:d;s=(s=t.makeArray(t(l).find(h)))[s.length-1]}var p=t.Event(n.HIDE,{relatedTarget:this._element}),g=t.Event(n.SHOW,{relatedTarget:s});if(s&&t(s).trigger(p),t(this._element).trigger(g),!g.isDefaultPrevented()&&!p.isDefaultPrevented()){c&&(i=t(c)[0]),this._activate(this._element,l);var m=function(){var i=t.Event(n.HIDDEN,{relatedTarget:e._element}),r=t.Event(n.SHOWN,{relatedTarget:s});t(s).trigger(i),t(e._element).trigger(r)};i?this._activate(i,i.parentNode,m):m()}}},p.dispose=function(){t.removeData(this._element,"bs.tab"),this._element=null},p._activate=function(e,n,i){var s=this,r=("UL"===n.nodeName?t(n).find(f):t(n).children(d))[0],a=i&&o.supportsTransitionEnd()&&r&&t(r).hasClass(l),c=function(){return s._transitionComplete(e,r,i)};r&&a?t(r).one(o.TRANSITION_END,c).emulateTransitionEnd(150):c()},p._transitionComplete=function(e,n,s){if(n){t(n).removeClass(c+" "+r);var a=t(n.parentNode).find(m)[0];a&&t(a).removeClass(r),"tab"===n.getAttribute("role")&&n.setAttribute("aria-selected",!1)}if(t(e).addClass(r),"tab"===e.getAttribute("role")&&e.setAttribute("aria-selected",!0),o.reflow(e),t(e).addClass(c),e.parentNode&&t(e.parentNode).hasClass(i)){var l=t(e).closest(h)[0];l&&t(l).find(g).addClass(r),e.setAttribute("aria-expanded",!0)}s&&s()},e._jQueryInterface=function(n){return this.each(function(){var i=t(this),s=i.data("bs.tab");if(s||(s=new e(this),i.data("bs.tab",s)),"string"==typeof n){if(void 0===s[n])throw new TypeError('No method named "'+n+'"');s[n]()}})},s(e,null,[{key:"VERSION",get:function(){return"4.0.0"}}]),e}();return t(document).on(n.CLICK_DATA_API,p,function(e){e.preventDefault(),v._jQueryInterface.call(t(this),"show")}),t.fn.tab=v._jQueryInterface,t.fn.tab.Constructor=v,t.fn.tab.noConflict=function(){return t.fn.tab=e,v._jQueryInterface},v}(e);!function(t){if(void 0===t)throw new TypeError("Bootstrap's JavaScript requires jQuery. jQuery must be included before Bootstrap's JavaScript.");var e=t.fn.jquery.split(" ")[0].split(".");if(e[0]<2&&e[1]<9||1===e[0]&&9===e[1]&&e[2]<1||e[0]>=4)throw new Error("Bootstrap's JavaScript requires at least jQuery v1.9.1 but less than v4.0.0")}(e),t.Util=o,t.Alert=a,t.Button=l,t.Carousel=c,t.Collapse=h,t.Dropdown=u,t.Modal=d,t.Popover=p,t.Scrollspy=g,t.Tab=m,t.Tooltip=f,Object.defineProperty(t,"__esModule",{value:!0})}),function(){var t=[].slice;!function(e,n){"use strict";var i;i=function(){function t(t,n){var i;null==n&&(n={}),this.$element=e(t),this.options=e.extend({},e.fn.bootstrapSwitch.defaults,{state:this.$element.is(":checked"),size:this.$element.data("size"),animate:this.$element.data("animate"),disabled:this.$element.is(":disabled"),readonly:this.$element.is("[readonly]"),indeterminate:this.$element.data("indeterminate"),onColor:this.$element.data("on-color"),offColor:this.$element.data("off-color"),onText:this.$element.data("on-text"),offText:this.$element.data("off-text"),labelText:this.$element.data("label-text"),baseClass:this.$element.data("base-class"),wrapperClass:this.$element.data("wrapper-class"),radioAllOff:this.$element.data("radio-all-off")},n),this.$wrapper=e("<div>",{class:(i=this,function(){var t;return(t=[""+i.options.baseClass].concat(i._getClasses(i.options.wrapperClass))).push(i.options.state?i.options.baseClass+"-on":i.options.baseClass+"-off"),null!=i.options.size&&t.push(i.options.baseClass+"-"+i.options.size),i.options.animate&&t.push(i.options.baseClass+"-animate"),i.options.disabled&&t.push(i.options.baseClass+"-disabled"),i.options.readonly&&t.push(i.options.baseClass+"-readonly"),i.options.indeterminate&&t.push(i.options.baseClass+"-indeterminate"),i.$element.attr("id")&&t.push(i.options.baseClass+"-id-"+i.$element.attr("id")),t.join(" ")})()}),this.$container=e("<div>",{class:this.options.baseClass+"-container"}),this.$on=e("<span>",{html:this.options.onText,class:this.options.baseClass+"-handle-on "+this.options.baseClass+"-"+this.options.onColor}),this.$off=e("<span>",{html:this.options.offText,class:this.options.baseClass+"-handle-off "+this.options.baseClass+"-"+this.options.offColor}),this.$label=e("<label>",{html:this.options.labelText,class:this.options.baseClass+"-label"}),this.options.indeterminate&&this.$element.prop("indeterminate",!0),this.$element.on("init.bootstrapSwitch",function(e){return function(){return e.options.onInit.apply(t,arguments)}}(this)),this.$element.on("switchChange.bootstrapSwitch",function(e){return function(){return e.options.onSwitchChange.apply(t,arguments)}}(this)),this.$container=this.$element.wrap(this.$container).parent(),this.$wrapper=this.$container.wrap(this.$wrapper).parent(),this.$element.before(this.$on).before(this.$label).before(this.$off).trigger("init.bootstrapSwitch"),this._elementHandlers(),this._handleHandlers(),this._labelHandlers(),this._formHandler()}return t.prototype._constructor=t,t.prototype.state=function(t,e){return void 0===t?this.options.state:this.options.disabled||this.options.readonly||this.options.indeterminate?this.$element:this.options.state&&!this.options.radioAllOff&&this.$element.is(":radio")?this.$element:(t=!!t,this.$element.prop("checked",t).trigger("change.bootstrapSwitch",e),this.$element)},t.prototype.toggleState=function(t){return this.options.disabled||this.options.readonly||this.options.indeterminate?this.$element:this.$element.prop("checked",!this.options.state).trigger("change.bootstrapSwitch",t)},t.prototype.size=function(t){return void 0===t?this.options.size:(null!=this.options.size&&this.$wrapper.removeClass(this.options.baseClass+"-"+this.options.size),t&&this.$wrapper.addClass(this.options.baseClass+"-"+t),this.options.size=t,this.$element)},t.prototype.animate=function(t){return void 0===t?this.options.animate:(t=!!t,this.$wrapper[t?"addClass":"removeClass"](this.options.baseClass+"-animate"),this.options.animate=t,this.$element)},t.prototype.disabled=function(t){return void 0===t?this.options.disabled:(t=!!t,this.$wrapper[t?"addClass":"removeClass"](this.options.baseClass+"-disabled"),this.$element.prop("disabled",t),this.options.disabled=t,this.$element)},t.prototype.toggleDisabled=function(){return this.$element.prop("disabled",!this.options.disabled),this.$wrapper.toggleClass(this.options.baseClass+"-disabled"),this.options.disabled=!this.options.disabled,this.$element},t.prototype.readonly=function(t){return void 0===t?this.options.readonly:(t=!!t,this.$wrapper[t?"addClass":"removeClass"](this.options.baseClass+"-readonly"),this.$element.prop("readonly",t),this.options.readonly=t,this.$element)},t.prototype.toggleReadonly=function(){return this.$element.prop("readonly",!this.options.readonly),this.$wrapper.toggleClass(this.options.baseClass+"-readonly"),this.options.readonly=!this.options.readonly,this.$element},t.prototype.indeterminate=function(t){return void 0===t?this.options.indeterminate:(t=!!t,this.$wrapper[t?"addClass":"removeClass"](this.options.baseClass+"-indeterminate"),this.$element.prop("indeterminate",t),this.options.indeterminate=t,this.$element)},t.prototype.toggleIndeterminate=function(){return this.$element.prop("indeterminate",!this.options.indeterminate),this.$wrapper.toggleClass(this.options.baseClass+"-indeterminate"),this.options.indeterminate=!this.options.indeterminate,this.$element},t.prototype.onColor=function(t){var e;return e=this.options.onColor,void 0===t?e:(null!=e&&this.$on.removeClass(this.options.baseClass+"-"+e),this.$on.addClass(this.options.baseClass+"-"+t),this.options.onColor=t,this.$element)},t.prototype.offColor=function(t){var e;return e=this.options.offColor,void 0===t?e:(null!=e&&this.$off.removeClass(this.options.baseClass+"-"+e),this.$off.addClass(this.options.baseClass+"-"+t),this.options.offColor=t,this.$element)},t.prototype.onText=function(t){return void 0===t?this.options.onText:(this.$on.html(t),this.options.onText=t,this.$element)},t.prototype.offText=function(t){return void 0===t?this.options.offText:(this.$off.html(t),this.options.offText=t,this.$element)},t.prototype.labelText=function(t){return void 0===t?this.options.labelText:(this.$label.html(t),this.options.labelText=t,this.$element)},t.prototype.baseClass=function(t){return this.options.baseClass},t.prototype.wrapperClass=function(t){return void 0===t?this.options.wrapperClass:(t||(t=e.fn.bootstrapSwitch.defaults.wrapperClass),this.$wrapper.removeClass(this._getClasses(this.options.wrapperClass).join(" ")),this.$wrapper.addClass(this._getClasses(t).join(" ")),this.options.wrapperClass=t,this.$element)},t.prototype.radioAllOff=function(t){return void 0===t?this.options.radioAllOff:(this.options.radioAllOff=t,this.$element)},t.prototype.onInit=function(t){return void 0===t?this.options.onInit:(t||(t=e.fn.bootstrapSwitch.defaults.onInit),this.options.onInit=t,this.$element)},t.prototype.onSwitchChange=function(t){return void 0===t?this.options.onSwitchChange:(t||(t=e.fn.bootstrapSwitch.defaults.onSwitchChange),this.options.onSwitchChange=t,this.$element)},t.prototype.destroy=function(){var t;return(t=this.$element.closest("form")).length&&t.off("reset.bootstrapSwitch").removeData("bootstrap-switch"),this.$container.children().not(this.$element).remove(),this.$element.unwrap().unwrap().off(".bootstrapSwitch").removeData("bootstrap-switch"),this.$element},t.prototype._elementHandlers=function(){return this.$element.on({"change.bootstrapSwitch":(t=this,function(n,i){var s;if(n.preventDefault(),n.stopImmediatePropagation(),(s=t.$element.is(":checked"))!==t.options.state)return t.options.state=s,t.$wrapper.removeClass(s?t.options.baseClass+"-off":t.options.baseClass+"-on").addClass(s?t.options.baseClass+"-on":t.options.baseClass+"-off"),i?void 0:(t.$element.is(":radio")&&e("[name='"+t.$element.attr("name")+"']").not(t.$element).prop("checked",!1).trigger("change.bootstrapSwitch",!0),t.$element.trigger("switchChange.bootstrapSwitch",[s]))}),"focus.bootstrapSwitch":function(t){return function(e){return e.preventDefault(),t.$wrapper.addClass(t.options.baseClass+"-focused")}}(this),"blur.bootstrapSwitch":function(t){return function(e){return e.preventDefault(),t.$wrapper.removeClass(t.options.baseClass+"-focused")}}(this),"keydown.bootstrapSwitch":function(t){return function(e){if(e.which&&!t.options.disabled&&!t.options.readonly&&!t.options.indeterminate)switch(e.which){case 37:return e.preventDefault(),e.stopImmediatePropagation(),t.state(!1);case 39:return e.preventDefault(),e.stopImmediatePropagation(),t.state(!0)}}}(this)});var t},t.prototype._handleHandlers=function(){var t;return this.$on.on("click.bootstrapSwitch",(t=this,function(e){return t.state(!1),t.$element.trigger("focus.bootstrapSwitch")})),this.$off.on("click.bootstrapSwitch",function(t){return function(e){return t.state(!0),t.$element.trigger("focus.bootstrapSwitch")}}(this))},t.prototype._labelHandlers=function(){return this.$label.on({"mousemove.bootstrapSwitch touchmove.bootstrapSwitch":(t=this,function(e){var n;if(t.isLabelDragging)return e.preventDefault(),t.isLabelDragged=!0,n=((e.pageX||e.originalEvent.touches[0].pageX)-t.$wrapper.offset().left)/t.$wrapper.width()*100,t.options.animate&&t.$wrapper.removeClass(t.options.baseClass+"-animate"),n<25?n=25:n>75&&(n=75),t.$container.css("margin-left",n-75+"%"),t.$element.trigger("focus.bootstrapSwitch")}),"mousedown.bootstrapSwitch touchstart.bootstrapSwitch":function(t){return function(e){if(!(t.isLabelDragging||t.options.disabled||t.options.readonly||t.options.indeterminate))return e.preventDefault(),t.isLabelDragging=!0,t.$element.trigger("focus.bootstrapSwitch")}}(this),"mouseup.bootstrapSwitch touchend.bootstrapSwitch":function(t){return function(e){if(t.isLabelDragging)return e.preventDefault(),t.isLabelDragged?(t.isLabelDragged=!1,t.state(parseInt(t.$container.css("margin-left"),10)>-t.$container.width()/6),t.options.animate&&t.$wrapper.addClass(t.options.baseClass+"-animate"),t.$container.css("margin-left","")):t.state(!t.options.state),t.isLabelDragging=!1}}(this),"mouseleave.bootstrapSwitch":function(t){return function(e){return t.$label.trigger("mouseup.bootstrapSwitch")}}(this)});var t},t.prototype._formHandler=function(){var t;if(!(t=this.$element.closest("form")).data("bootstrap-switch"))return t.on("reset.bootstrapSwitch",function(){return n.setTimeout(function(){return t.find("input").filter(function(){return e(this).data("bootstrap-switch")}).each(function(){return e(this).bootstrapSwitch("state",this.checked)})},1)}).data("bootstrap-switch",!0)},t.prototype._getClasses=function(t){var n,i,s,r;if(!e.isArray(t))return[this.options.baseClass+"-"+t];for(i=[],s=0,r=t.length;s<r;s++)n=t[s],i.push(this.options.baseClass+"-"+n);return i},t}(),e.fn.bootstrapSwitch=function(){var n,s,r;return s=arguments[0],n=2<=arguments.length?t.call(arguments,1):[],r=this,this.each(function(){var t,o;if((o=(t=e(this)).data("bootstrap-switch"))||t.data("bootstrap-switch",o=new i(this,s)),"string"==typeof s)return r=o[s].apply(o,n)}),r},e.fn.bootstrapSwitch.Constructor=i,e.fn.bootstrapSwitch.defaults={state:!0,size:null,animate:!0,disabled:!1,readonly:!1,indeterminate:!1,onColor:"primary",offColor:"default",onText:"ON",offText:"OFF",labelText:"&nbsp;",baseClass:"bootstrap-switch",wrapperClass:"wrapper",radioAllOff:!1,onInit:function(){},onSwitchChange:function(){}}}(window.jQuery,window)}.call(this),function(t){"use strict";var e={tagClass:function(t){return"badge badge-info"},focusClass:"focus",itemValue:function(t){return t?t.toString():t},itemText:function(t){return this.itemValue(t)},itemTitle:function(t){return null},freeInput:!0,addOnBlur:!0,maxTags:void 0,maxChars:void 0,confirmKeys:[13,44],delimiter:",",delimiterRegex:null,cancelConfirmKeysOnEmpty:!1,onTagExists:function(t,e){e.hide().fadeIn()},trimValue:!1,allowDuplicates:!1,triggerChange:!0};function n(e,n){this.isInit=!0,this.itemsArray=[],this.$element=t(e),this.$element.hide(),this.isSelect="SELECT"===e.tagName,this.multiple=this.isSelect&&e.hasAttribute("multiple"),this.objectItems=n&&n.itemValue,this.placeholderText=e.hasAttribute("placeholder")?this.$element.attr("placeholder"):"",this.inputSize=Math.max(1,this.placeholderText.length),this.$container=t('<div class="bootstrap-tagsinput"></div>'),this.$input=t('<input type="text" placeholder="'+this.placeholderText+'"/>').appendTo(this.$container),this.$element.before(this.$container),this.build(n),this.isInit=!1}function i(t,e){if("function"!=typeof t[e]){var n=t[e];t[e]=function(t){return t[n]}}}function s(t,e){if("function"!=typeof t[e]){var n=t[e];t[e]=function(){return n}}}n.prototype={constructor:n,add:function(e,n,i){var s=this;if(!(s.options.maxTags&&s.itemsArray.length>=s.options.maxTags)&&(!1===e||e)){if("string"==typeof e&&s.options.trimValue&&(e=t.trim(e)),"object"==typeof e&&!s.objectItems)throw"Can't add objects when itemValue option is not set";if(!e.toString().match(/^\s*$/)){if(s.isSelect&&!s.multiple&&s.itemsArray.length>0&&s.remove(s.itemsArray[0]),"string"==typeof e&&"INPUT"===this.$element[0].tagName){var r=s.options.delimiterRegex?s.options.delimiterRegex:s.options.delimiter,a=e.split(r);if(a.length>1){for(var l=0;l<a.length;l++)this.add(a[l],!0);return void(n||s.pushVal(s.options.triggerChange))}}var c=s.options.itemValue(e),h=s.options.itemText(e),u=s.options.tagClass(e),d=s.options.itemTitle(e),f=t.grep(s.itemsArray,function(t){return s.options.itemValue(t)===c})[0];if(!f||s.options.allowDuplicates){if(!(s.items().toString().length+e.length+1>s.options.maxInputLength)){var p=t.Event("beforeItemAdd",{item:e,cancel:!1,options:i});if(s.$element.trigger(p),!p.cancel){s.itemsArray.push(e);var g=t('<span class="badge '+o(u)+(null!==d?'" title="'+d:"")+'">'+o(h)+'<span data-role="remove"></span></span>');g.data("item",e),s.findInputWrapper().before(g),g.after(" ");var m=t('option[value="'+encodeURIComponent(c)+'"]',s.$element).length||t('option[value="'+o(c)+'"]',s.$element).length;if(s.isSelect&&!m){var v=t("<option selected>"+o(h)+"</option>");v.data("item",e),v.attr("value",c),s.$element.append(v)}n||s.pushVal(s.options.triggerChange),s.options.maxTags!==s.itemsArray.length&&s.items().toString().length!==s.options.maxInputLength||s.$container.addClass("bootstrap-tagsinput-max"),t(".typeahead, .twitter-typeahead",s.$container).length&&s.$input.typeahead("val",""),this.isInit?s.$element.trigger(t.Event("itemAddedOnInit",{item:e,options:i})):s.$element.trigger(t.Event("itemAdded",{item:e,options:i}))}}}else if(s.options.onTagExists){var b=t(".badge",s.$container).filter(function(){return t(this).data("item")===f});s.options.onTagExists(e,b)}}}},remove:function(e,n,i){var s=this;if(s.objectItems&&(e=(e="object"==typeof e?t.grep(s.itemsArray,function(t){return s.options.itemValue(t)==s.options.itemValue(e)}):t.grep(s.itemsArray,function(t){return s.options.itemValue(t)==e}))[e.length-1]),e){var r=t.Event("beforeItemRemove",{item:e,cancel:!1,options:i});if(s.$element.trigger(r),r.cancel)return;t(".badge",s.$container).filter(function(){return t(this).data("item")===e}).remove(),t("option",s.$element).filter(function(){return t(this).data("item")===e}).remove(),-1!==t.inArray(e,s.itemsArray)&&s.itemsArray.splice(t.inArray(e,s.itemsArray),1)}n||s.pushVal(s.options.triggerChange),s.options.maxTags>s.itemsArray.length&&s.$container.removeClass("bootstrap-tagsinput-max"),s.$element.trigger(t.Event("itemRemoved",{item:e,options:i}))},removeAll:function(){for(t(".badge",this.$container).remove(),t("option",this.$element).remove();this.itemsArray.length>0;)this.itemsArray.pop();this.pushVal(this.options.triggerChange)},refresh:function(){var e=this;t(".badge",e.$container).each(function(){var n=t(this),i=n.data("item"),s=e.options.itemValue(i),r=e.options.itemText(i),a=e.options.tagClass(i);(n.attr("class",null),n.addClass("badge "+o(a)),n.contents().filter(function(){return 3==this.nodeType})[0].nodeValue=o(r),e.isSelect)&&t("option",e.$element).filter(function(){return t(this).data("item")===i}).attr("value",s)})},items:function(){return this.itemsArray},pushVal:function(){var e=this,n=t.map(e.items(),function(t){return e.options.itemValue(t).toString()});e.$element.val(n,!0),e.options.triggerChange&&e.$element.trigger("change")},build:function(n){var r=this;if(r.options=t.extend({},e,n),r.objectItems&&(r.options.freeInput=!1),i(r.options,"itemValue"),i(r.options,"itemText"),s(r.options,"tagClass"),r.options.typeahead){var o=r.options.typeahead||{};s(o,"source"),r.$input.typeahead(t.extend({},o,{source:function(e,n){function i(t){for(var e=[],i=0;i<t.length;i++){var o=r.options.itemText(t[i]);s[o]=t[i],e.push(o)}n(e)}this.map={};var s=this.map,a=o.source(e);t.isFunction(a.success)?a.success(i):t.isFunction(a.then)?a.then(i):t.when(a).then(i)},updater:function(t){return r.add(this.map[t]),this.map[t]},matcher:function(t){return-1!==t.toLowerCase().indexOf(this.query.trim().toLowerCase())},sorter:function(t){return t.sort()},highlighter:function(t){var e=new RegExp("("+this.query+")","gi");return t.replace(e,"<strong>$1</strong>")}}))}if(r.options.typeaheadjs){var l=r.options.typeaheadjs;t.isArray(l)||(l=[null,l]),t.fn.typeahead.apply(r.$input,l).on("typeahead:selected",t.proxy(function(t,e,n){var i=0;l.some(function(t,e){return t.name===n&&(i=e,!0)}),l[i].valueKey?r.add(e[l[i].valueKey]):r.add(e),r.$input.typeahead("val","")},r))}r.$container.on("click",t.proxy(function(t){r.$element.attr("disabled")||r.$input.removeAttr("disabled"),r.$input.focus()},r)),r.options.addOnBlur&&r.options.freeInput&&r.$input.on("focusout",t.proxy(function(e){0===t(".typeahead, .twitter-typeahead",r.$container).length&&(r.add(r.$input.val()),r.$input.val(""))},r)),r.$container.on({focusin:function(){r.$container.addClass(r.options.focusClass)},focusout:function(){r.$container.removeClass(r.options.focusClass)}}),r.$container.on("keydown","input",t.proxy(function(e){var n=t(e.target),i=r.findInputWrapper();if(r.$element.attr("disabled"))r.$input.attr("disabled","disabled");else{switch(e.which){case 8:if(0===a(n[0])){var s=i.prev();s.length&&r.remove(s.data("item"))}break;case 46:if(0===a(n[0])){var o=i.next();o.length&&r.remove(o.data("item"))}break;case 37:var l=i.prev();0===n.val().length&&l[0]&&(l.before(i),n.focus());break;case 39:var c=i.next();0===n.val().length&&c[0]&&(c.after(i),n.focus())}var h=n.val().length,u=h+Math.ceil(h/5)+1;n.attr("size",Math.max(this.inputSize,u))}},r)),r.$container.on("keypress","input",t.proxy(function(e){var n=t(e.target);if(r.$element.attr("disabled"))r.$input.attr("disabled","disabled");else{var i,s,o,a=n.val(),l=r.options.maxChars&&a.length>=r.options.maxChars;r.options.freeInput&&(i=e,s=r.options.confirmKeys,o=!1,t.each(s,function(t,e){if("number"==typeof e&&i.which===e)return o=!0,!1;if(i.which===e.which){var n=!e.hasOwnProperty("altKey")||i.altKey===e.altKey,s=!e.hasOwnProperty("shiftKey")||i.shiftKey===e.shiftKey,r=!e.hasOwnProperty("ctrlKey")||i.ctrlKey===e.ctrlKey;if(n&&s&&r)return o=!0,!1}}),o||l)&&(0!==a.length&&(r.add(l?a.substr(0,r.options.maxChars):a),n.val("")),!1===r.options.cancelConfirmKeysOnEmpty&&e.preventDefault());var c=n.val().length,h=c+Math.ceil(c/5)+1;n.attr("size",Math.max(this.inputSize,h))}},r)),r.$container.on("click","[data-role=remove]",t.proxy(function(e){r.$element.attr("disabled")||r.remove(t(e.target).closest(".badge").data("item"))},r)),r.options.itemValue===e.itemValue&&("INPUT"===r.$element[0].tagName?r.add(r.$element.val()):t("option",r.$element).each(function(){r.add(t(this).attr("value"),!0)}))},destroy:function(){this.$container.off("keypress","input"),this.$container.off("click","[role=remove]"),this.$container.remove(),this.$element.removeData("tagsinput"),this.$element.show()},focus:function(){this.$input.focus()},input:function(){return this.$input},findInputWrapper:function(){for(var e=this.$input[0],n=this.$container[0];e&&e.parentNode!==n;)e=e.parentNode;return t(e)}},t.fn.tagsinput=function(e,i,s){var r=[];return this.each(function(){var o=t(this).data("tagsinput");if(o)if(e||i){if(void 0!==o[e]){if(3===o[e].length&&void 0!==s)var a=o[e](i,null,s);else a=o[e](i);void 0!==a&&r.push(a)}}else r.push(o);else o=new n(this,e),t(this).data("tagsinput",o),r.push(o),"SELECT"===this.tagName&&t("option",t(this)).attr("selected","selected"),t(this).val(t(this).val())}),"string"==typeof e?r.length>1?r:r[0]:r},t.fn.tagsinput.Constructor=n;var r=t("<div />");function o(t){return t?r.text(t).html():""}function a(t){var e=0;if(document.selection){t.focus();var n=document.selection.createRange();n.moveStart("character",-t.value.length),e=n.text.length}else(t.selectionStart||"0"==t.selectionStart)&&(e=t.selectionStart);return e}t(function(){t("input[data-role=tagsinput], select[multiple][data-role=tagsinput]").tagsinput()})}(window.jQuery),function(t,e,n){e.onDomReady=n}(0,this,function(t){"use strict";function e(t){if(!w){if(!o.body)return s(e);for(w=!0;t=C.shift();)s(t)}}function n(t){(y||t.type===l||o[d]===u)&&(i(),e())}function i(){y?(o[b](m,n,c),t[b](l,n,c)):(o[p](v,n),t[p](h,n))}function s(t,e){setTimeout(t,+e>=0?e:1)}function r(t){w?s(t):C.push(t)}null==document.readyState&&document.addEventListener&&(document.addEventListener("DOMContentLoaded",function t(){document.removeEventListener("DOMContentLoaded",t,!1),document.readyState="complete"},!1),document.readyState="loading");var o=t.document,a=o.documentElement,l="load",c=!1,h="on"+l,u="complete",d="readyState",f="attachEvent",p="detachEvent",g="addEventListener",m="DOMContentLoaded",v="onreadystatechange",b="removeEventListener",y=g in o,_=c,w=c,C=[];if(o[d]===u)s(e);else if(y)o[g](m,n,c),t[g](l,n,c);else{o[f](v,n),t[f](h,n);try{_=null==t.frameElement&&a}catch(t){}_&&_.doScroll&&function t(){if(!w){try{_.doScroll("left")}catch(e){return s(t,50)}i(),e()}}()}return r.version="1.4.0",r.isReady=function(){return w},r}(this)),document.querySelectorAll||(document.querySelectorAll=function(t){var e,n=document.createElement("style"),i=[];for(document.documentElement.firstChild.appendChild(n),document._qsa=[],n.styleSheet.cssText=t+"{x-qsa:expression(document._qsa && document._qsa.push(this))}",window.scrollBy(0,0),n.parentNode.removeChild(n);document._qsa.length;)(e=document._qsa.shift()).style.removeAttribute("x-qsa"),i.push(e);return document._qsa=null,i}),document.querySelector||(document.querySelector=function(t){var e=document.querySelectorAll(t);return e.length?e[0]:null}),document.getElementsByClassName||(document.getElementsByClassName=function(t){return t=String(t).replace(/^|\s+/g,"."),document.querySelectorAll(t)}),Object.keys||(Object.keys=function(t){if(t!==Object(t))throw TypeError("Object.keys called on non-object");var e,n=[];for(e in t)Object.prototype.hasOwnProperty.call(t,e)&&n.push(e);return n}),function(t){var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";t.atob=t.atob||function(t){var n=0,i=[],s=0,r=0;if((t=(t=String(t)).replace(/\s/g,"")).length%4==0&&(t=t.replace(/=+$/,"")),t.length%4==1)throw Error("InvalidCharacterError");if(/[^+/0-9A-Za-z]/.test(t))throw Error("InvalidCharacterError");for(;n<t.length;)s=s<<6|e.indexOf(t.charAt(n)),24===(r+=6)&&(i.push(String.fromCharCode(s>>16&255)),i.push(String.fromCharCode(s>>8&255)),i.push(String.fromCharCode(255&s)),r=0,s=0),n+=1;return 12===r?(s>>=4,i.push(String.fromCharCode(255&s))):18===r&&(s>>=2,i.push(String.fromCharCode(s>>8&255)),i.push(String.fromCharCode(255&s))),i.join("")},t.btoa=t.btoa||function(t){t=String(t);var n,i,s,r,o,a,l,c=0,h=[];if(/[^\x00-\xFF]/.test(t))throw Error("InvalidCharacterError");for(;c<t.length;)r=(n=t.charCodeAt(c++))>>2,o=(3&n)<<4|(i=t.charCodeAt(c++))>>4,a=(15&i)<<2|(s=t.charCodeAt(c++))>>6,l=63&s,c===t.length+2?(a=64,l=64):c===t.length+1&&(l=64),h.push(e.charAt(r),e.charAt(o),e.charAt(a),e.charAt(l));return h.join("")}}(this),function(){function t(e,n,i){e.document;var s,r=e.currentStyle[n].match(/([\d\.]+)(%|cm|em|in|mm|pc|pt|)/)||[0,0,""],o=r[1],a=r[2];return i=i?/%|em/.test(a)&&e.parentElement?t(e.parentElement,"fontSize",null):16:i,s="fontSize"==n?i:/width/i.test(n)?e.clientWidth:e.clientHeight,"%"==a?o/100*s:"cm"==a?.3937*o*96:"em"==a?o*i:"in"==a?96*o:"mm"==a?.3937*o*96/10:"pc"==a?12*o*96/72:"pt"==a?96*o/72:o}function e(t,e){var n="border"==e?"Width":"",i=e+"Top"+n,s=e+"Right"+n,r=e+"Bottom"+n,o=e+"Left"+n;t[e]=(t[i]==t[s]&&t[i]==t[r]&&t[i]==t[o]?[t[i]]:t[i]==t[r]&&t[o]==t[s]?[t[i],t[s]]:t[o]==t[s]?[t[i],t[s],t[r]]:[t[i],t[s],t[r],t[o]]).join(" ")}function n(n){var i,s=this,r=n.currentStyle,o=t(n,"fontSize"),a=function(t){return"-"+t.toLowerCase()};for(i in r)if(Array.prototype.push.call(s,"styleFloat"==i?"float":i.replace(/[A-Z]/,a)),"width"==i)s[i]=n.offsetWidth+"px";else if("height"==i)s[i]=n.offsetHeight+"px";else if("styleFloat"==i)s.float=r[i];else if(/margin.|padding.|border.+W/.test(i)&&"auto"!=s[i])s[i]=Math.round(t(n,i,o))+"px";else if(/^outline/.test(i))try{s[i]=r[i]}catch(t){s.outlineColor=r.color,s.outlineStyle=s.outlineStyle||"none",s.outlineWidth=s.outlineWidth||"0px",s.outline=[s.outlineColor,s.outlineWidth,s.outlineStyle].join(" ")}else s[i]=r[i];e(s,"margin"),e(s,"padding"),e(s,"border"),s.fontSize=Math.round(o)+"px"}window.getComputedStyle||(n.prototype={constructor:n,getPropertyPriority:function(){throw new Error("NotSupportedError: DOM Exception 9")},getPropertyValue:function(t){return this[t.replace(/-([a-z])/g,function(t){return(t=t.charAt?t.split(""):t)[1].toUpperCase()})]},item:function(t){return this[t]},removeProperty:function(){throw new Error("NoModificationAllowedError: DOM Exception 7")},setProperty:function(){throw new Error("NoModificationAllowedError: DOM Exception 7")},getPropertyCSSValue:function(){throw new Error("NotSupportedError: DOM Exception 9")}},window.getComputedStyle=function(t){return new n(t)})}(),Object.prototype.hasOwnProperty||(Object.prototype.hasOwnProperty=function(t){var e=this.__proto__||this.constructor.prototype;return t in this&&(!(t in e)||e[t]!==this[t])}),function(t,e){t.augment=function(){"use strict";var t=function(){},e=Array.prototype.slice,n=function(n,i){var s=t.prototype="function"==typeof n?n.prototype:n,r=new t,o=i.apply(r,e.call(arguments,2).concat(s));if("object"==typeof o)for(var a in o)r[a]=o[a];if(!r.hasOwnProperty("constructor"))return r;var l=r.constructor;return l.prototype=r,l};return n.defclass=function(t){var e=t.constructor;return e.prototype=t,e},n.extend=function(t,e){return n(t,function(t){return this.uber=t,e})},n}()}(this),function(t,e){function n(t,e,n,r){var o=i(n.substr(n.lastIndexOf(t.domain)),t);o&&s(null,r,o,e)}function i(t,e){for(var n={theme:p(x.settings.themes.gray,null),stylesheets:e.stylesheets,holderURL:[]},i=!1,s=String.fromCharCode(11),r=t.replace(/([^\\])\//g,"$1"+s).split(s),o=/%[0-9a-f]{2}/gi,a=r.length,l=0;a>l;l++){var c=r[l];if(c.match(o))try{c=decodeURIComponent(c)}catch(t){c=r[l]}var h=!1;if(x.flags.dimensions.match(c))i=!0,n.dimensions=x.flags.dimensions.output(c),h=!0;else if(x.flags.fluid.match(c))i=!0,n.dimensions=x.flags.fluid.output(c),n.fluid=!0,h=!0;else if(x.flags.textmode.match(c))n.textmode=x.flags.textmode.output(c),h=!0;else if(x.flags.colors.match(c)){var u=x.flags.colors.output(c);n.theme=p(n.theme,u),h=!0}else if(e.themes[c])e.themes.hasOwnProperty(c)&&(n.theme=p(e.themes[c],null)),h=!0;else if(x.flags.font.match(c))n.font=x.flags.font.output(c),h=!0;else if(x.flags.auto.match(c))n.auto=!0,h=!0;else if(x.flags.text.match(c))n.text=x.flags.text.output(c),h=!0;else if(x.flags.random.match(c)){null==x.vars.cache.themeKeys&&(x.vars.cache.themeKeys=Object.keys(e.themes));var d=x.vars.cache.themeKeys[0|Math.random()*x.vars.cache.themeKeys.length];n.theme=p(e.themes[d],null),h=!0}h&&n.holderURL.push(c)}return n.holderURL.unshift(e.domain),n.holderURL=n.holderURL.join("/"),!!i&&n}function s(t,e,n,i){var s=n.dimensions,o=n.theme,l=s.width+"x"+s.height;if(t=null==t?n.fluid?"fluid":"image":t,null!=n.text&&(o.text=n.text,"object"===e.nodeName.toLowerCase())){for(var h=o.text.split("\\n"),d=0;d<h.length;d++)h[d]=y(h[d]);o.text=h.join("\\n")}var f=n.holderURL,g=p(i,null);n.font&&(o.font=n.font,!g.noFontFallback&&"img"===e.nodeName.toLowerCase()&&x.setup.supportsCanvas&&"svg"===g.renderer&&(g=p(g,{renderer:"canvas"}))),n.font&&"canvas"==g.renderer&&(g.reRender=!0),"background"==t?null==e.getAttribute("data-background-src")&&u(e,{"data-background-src":f}):u(e,{"data-src":f}),n.theme=o,e.holderData={flags:n,renderSettings:g},("image"==t||"fluid"==t)&&u(e,{alt:o.text?(o.text.length>16?o.text.substring(0,16)+"…":o.text)+" ["+l+"]":l}),"image"==t?("html"!=g.renderer&&n.auto||(e.style.width=s.width+"px",e.style.height=s.height+"px"),"html"==g.renderer?e.style.backgroundColor=o.background:(r(t,{dimensions:s,theme:o,flags:n},e,g),n.textmode&&"exact"==n.textmode&&(x.vars.resizableImages.push(e),a(e)))):"background"==t&&"html"!=g.renderer?r(t,{dimensions:s,theme:o,flags:n},e,g):"fluid"==t&&("%"==s.height.slice(-1)?e.style.height=s.height:null!=n.auto&&n.auto||(e.style.height=s.height+"px"),"%"==s.width.slice(-1)?e.style.width=s.width:null!=n.auto&&n.auto||(e.style.width=s.width+"px"),("inline"==e.style.display||""===e.style.display||"none"==e.style.display)&&(e.style.display="block"),c(e),"html"==g.renderer?e.style.backgroundColor=o.background:(x.vars.resizableImages.push(e),a(e)))}function r(t,e,n,i){function s(){var t=null;switch(i.renderer){case"canvas":t=D(l);break;case"svg":t=E(l,i);break;default:throw"Holder: invalid renderer: "+i.renderer}return t}var r;switch(i.renderer){case"svg":if(!x.setup.supportsSVG)return;break;case"canvas":if(!x.setup.supportsCanvas)return;break;default:return}var a={width:e.dimensions.width,height:e.dimensions.height,theme:e.theme,flags:e.flags},l=o(a);if(a.font.size,a.font.family,a.font.weight,null==(r=s()))throw"Holder: couldn't render placeholder";"background"==t?(n.style.backgroundImage="url("+r+")",n.style.backgroundSize=a.width+"px "+a.height+"px"):("img"===n.nodeName.toLowerCase()?u(n,{src:r}):"object"===n.nodeName.toLowerCase()&&(u(n,{data:r}),u(n,{type:"image/svg+xml"})),i.reRender&&setTimeout(function(){var t=s();if(null==t)throw"Holder: couldn't render placeholder";"img"===n.nodeName.toLowerCase()?u(n,{src:t}):"object"===n.nodeName.toLowerCase()&&(u(n,{data:t}),u(n,{type:"image/svg+xml"}))},100)),u(n,{"data-holder-rendered":!0})}function o(t){function e(t,e,n,i){e.width=n,e.height=i,t.width=Math.max(t.width,e.width),t.height+=e.height,t.add(e)}switch(t.font={family:t.theme.font?t.theme.font:"Arial, Helvetica, Open Sans, sans-serif",size:function(t,e,n){e=parseInt(e,10),t=parseInt(t,10);var i=Math.max(e,t),s=Math.min(e,t),r=x.defaults.scale,o=Math.min(.75*s,.75*i*r);return Math.round(Math.max(n,o))}(t.width,t.height,t.theme.size?t.theme.size:x.defaults.size),units:t.theme.units?t.theme.units:x.defaults.units,weight:t.theme.fontweight?t.theme.fontweight:"bold"},t.text=t.theme.text?t.theme.text:Math.floor(t.width)+"x"+Math.floor(t.height),t.flags.textmode){case"literal":t.text=t.flags.dimensions.width+"x"+t.flags.dimensions.height;break;case"exact":if(!t.flags.exactDimensions)break;t.text=Math.floor(t.flags.exactDimensions.width)+"x"+Math.floor(t.flags.exactDimensions.height)}var n=new I({width:t.width,height:t.height}),i=n.Shape,s=new i.Rect("holderBg",{fill:t.theme.background});s.resize(t.width,t.height),n.root.add(s);var r=new i.Group("holderTextGroup",{text:t.text,align:"center",font:t.font,fill:t.theme.foreground});r.moveTo(null,null,1),n.root.add(r);var o=r.textPositionData=T(n);if(!o)throw"Holder: staging fallback not supported yet.";r.properties.leading=o.boundingBox.height;var a=null,l=null;if(o.lineCount>1){var c=0,h=0,u=t.width*x.setup.lineWrapRatio,d=0;l=new i.Group("line"+d);for(var f=0;f<o.words.length;f++){var p=o.words[f];a=new i.Text(p.text);var g="\\n"==p.text;(c+p.width>=u||!0===g)&&(e(r,l,c,r.properties.leading),c=0,h+=r.properties.leading,d+=1,(l=new i.Group("line"+d)).y=h),!0!==g&&(a.moveTo(c,0),c+=o.spaceWidth+p.width,l.add(a))}for(var m in e(r,l,c,r.properties.leading),r.children)(l=r.children[m]).moveTo((r.width-l.width)/2,null,null);r.moveTo((t.width-r.width)/2,(t.height-r.height)/2,null),(t.height-r.height)/2<0&&r.moveTo(null,0,null)}else a=new i.Text(t.text),(l=new i.Group("line0")).add(a),r.add(l),r.moveTo((t.width-o.boundingBox.width)/2,(t.height-o.boundingBox.height)/2,null);return n}function a(t){var e;for(var n in e=null==t||null==t.nodeType?x.vars.resizableImages:[t])if(e.hasOwnProperty(n)){var i=e[n];if(i.holderData){var s=i.holderData.flags,o=l(i,S.invisibleErrorFn(a));if(o){if(s.fluid&&s.auto){var c=i.holderData.fluidConfig;switch(c.mode){case"width":o.height=o.width/c.ratio;break;case"height":o.width=o.height*c.ratio}}var h={dimensions:o,theme:s.theme,flags:s};s.textmode&&"exact"==s.textmode&&(s.exactDimensions=o,h.dimensions=s.dimensions),r("image",h,i,i.holderData.renderSettings)}}}}function l(t,e){var n={height:t.clientHeight,width:t.clientWidth};return n.height||n.width?(t.removeAttribute("data-holder-invisible"),n):(u(t,{"data-holder-invisible":!0}),void e.call(this,t))}function c(t){if(t.holderData){var e=l(t,S.invisibleErrorFn(c));if(e){var n=t.holderData.flags,i={fluidHeight:"%"==n.dimensions.height.slice(-1),fluidWidth:"%"==n.dimensions.width.slice(-1),mode:null,initialDimensions:e};i.fluidWidth&&!i.fluidHeight?(i.mode="width",i.ratio=i.initialDimensions.width/parseFloat(n.dimensions.height)):!i.fluidWidth&&i.fluidHeight&&(i.mode="height",i.ratio=parseFloat(n.dimensions.width)/i.initialDimensions.height),t.holderData.fluidConfig=i}}}function h(t,e){return null==e?C.createElement(t):C.createElementNS(e,t)}function u(t,e){for(var n in e)t.setAttribute(n,e[n])}function d(t,e,n){if(null==t){t=h("svg",w);var i=h("defs",w);t.appendChild(i)}return t.webkitMatchesSelector&&t.setAttribute("xmlns",w),u(t,{width:e,height:n,viewBox:"0 0 "+e+" "+n,preserveAspectRatio:"none"}),t}function f(t,n){if(e.XMLSerializer){var i=new XMLSerializer,s="",r=n.stylesheets;if(t.querySelector("defs"),n.svgXMLStylesheet){for(var o=(new DOMParser).parseFromString("<xml />","application/xml"),a=r.length-1;a>=0;a--){var l=o.createProcessingInstruction("xml-stylesheet",'href="'+r[a]+'" rel="stylesheet"');o.insertBefore(l,o.firstChild)}var c=o.createProcessingInstruction("xml",'version="1.0" encoding="UTF-8" standalone="yes"');o.insertBefore(c,o.firstChild),o.removeChild(o.documentElement),s=i.serializeToString(o)}var h=i.serializeToString(t);return s+(h=h.replace(/\&amp;(\#[0-9]{2,}\;)/g,"&$1"))}}function p(t,e){var n={};for(var i in t)t.hasOwnProperty(i)&&(n[i]=t[i]);if(null!=e)for(var s in e)e.hasOwnProperty(s)&&(n[s]=e[s]);return n}function g(t){var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(n+":"+t[n]);return e.join(";")}function m(){!function(t){x.vars.debounceTimer||t.call(this),x.vars.debounceTimer&&clearTimeout(x.vars.debounceTimer),x.vars.debounceTimer=setTimeout(function(){x.vars.debounceTimer=null,t.call(this)},x.setup.debounce)}(function(){a(null)})}function v(t){var n=null;return"string"==typeof t?n=C.querySelectorAll(t):e.NodeList&&t instanceof e.NodeList?n=t:e.Node&&t instanceof e.Node?n=[t]:e.HTMLCollection&&t instanceof e.HTMLCollection?n=t:null===t&&(n=[]),n}function b(t,e){var n=new Image;n.onerror=function(){e.call(this,!1)},n.onload=function(){e.call(this,!0)},n.src=t}function y(t){for(var e=[],n=0,i=t.length-1;i>=0;i--)(n=t.charCodeAt(i))>128?e.unshift(["&#",n,";"].join("")):e.unshift(t[i]);return e.join("")}function _(t){return t.replace(/&#(\d+);/g,function(t,e){return String.fromCharCode(e)})}var w="http://www.w3.org/2000/svg",C=e.document,S={addTheme:function(t,e){return null!=t&&null!=e&&(x.settings.themes[t]=e),delete x.vars.cache.themeKeys,this},addImage:function(t,e){var n=C.querySelectorAll(e);if(n.length)for(var i=0,s=n.length;s>i;i++){var r=h("img");u(r,{"data-src":t}),n[i].appendChild(r)}return this},run:function(t){t=t||{};var r={};x.vars.preempted=!0;var o=p(x.settings,t);r.renderer=o.renderer?o.renderer:x.setup.renderer,-1===x.setup.renderers.join(",").indexOf(r.renderer)&&(r.renderer=x.setup.supportsSVG?"svg":x.setup.supportsCanvas?"canvas":"html"),o.use_canvas?r.renderer="canvas":o.use_svg&&(r.renderer="svg");var a=v(o.images),l=v(o.bgnodes),c=v(o.stylenodes),u=v(o.objects);r.stylesheets=[],r.svgXMLStylesheet=!0,r.noFontFallback=!!o.noFontFallback&&o.noFontFallback;for(var d=0;d<c.length;d++){var f=c[d];if(f.attributes.rel&&f.attributes.href&&"stylesheet"==f.attributes.rel.value){var g=f.attributes.href.value,m=h("a");m.href=g;var y=m.protocol+"//"+m.host+m.pathname+m.search;r.stylesheets.push(y)}}for(d=0;d<l.length;d++){var _=e.getComputedStyle(l[d],null).getPropertyValue("background-image"),w=l[d].getAttribute("data-background-src"),C=null;C=null==w?_:w;var S=null,T="?"+o.domain+"/";if(0===C.indexOf(T))S=C.slice(1);else if(-1!=C.indexOf(T)){var D=C.substr(C.indexOf(T)).slice(1).match(/([^\"]*)"?\)/);null!=D&&(S=D[1])}if(null!=S){var E=i(S,o);E&&s("background",l[d],E,r)}}for(d=0;d<u.length;d++){var I=u[d],A={};try{A.data=I.getAttribute("data"),A.dataSrc=I.getAttribute("data-src")}catch(t){}var k=null!=A.data&&0===A.data.indexOf(o.domain),O=null!=A.dataSrc&&0===A.dataSrc.indexOf(o.domain);k?n(o,r,A.data,I):O&&n(o,r,A.dataSrc,I)}for(d=0;d<a.length;d++){var N=a[d],P={};try{P.src=N.getAttribute("src"),P.dataSrc=N.getAttribute("data-src"),P.rendered=N.getAttribute("data-holder-rendered")}catch(t){}var L=null!=P.src,R=null!=P.dataSrc&&0===P.dataSrc.indexOf(o.domain),F=null!=P.rendered&&"true"==P.rendered;L?0===P.src.indexOf(o.domain)?n(o,r,P.src,N):R&&(F?n(o,r,P.dataSrc,N):function(t,e,i,s,r){b(t,function(t){t||n(e,i,s,r)})}(P.src,o,r,P.dataSrc,N)):R&&n(o,r,P.dataSrc,N)}return this},invisibleErrorFn:function(){return function(t){if(t.hasAttribute("data-holder-invisible"))throw"Holder: invisible placeholder"}}};S.add_theme=S.addTheme,S.add_image=S.addImage,S.invisible_error_fn=S.invisibleErrorFn;var x={settings:{domain:"holder.js",images:"img",objects:"object",bgnodes:"body .holderjs",stylenodes:"head link.holderjs",stylesheets:[],themes:{gray:{background:"#EEEEEE",foreground:"#AAAAAA"},social:{background:"#3a5a97",foreground:"#FFFFFF"},industrial:{background:"#434A52",foreground:"#C2F200"},sky:{background:"#0D8FDB",foreground:"#FFFFFF"},vine:{background:"#39DBAC",foreground:"#1E292C"},lava:{background:"#F8591A",foreground:"#1C2846"}}},defaults:{size:10,units:"pt",scale:1/16},flags:{dimensions:{regex:/^(\d+)x(\d+)$/,output:function(t){var e=this.regex.exec(t);return{width:+e[1],height:+e[2]}}},fluid:{regex:/^([0-9]+%?)x([0-9]+%?)$/,output:function(t){var e=this.regex.exec(t);return{width:e[1],height:e[2]}}},colors:{regex:/(?:#|\^)([0-9a-f]{3,})\:(?:#|\^)([0-9a-f]{3,})/i,output:function(t){var e=this.regex.exec(t);return{foreground:"#"+e[2],background:"#"+e[1]}}},text:{regex:/text\:(.*)/,output:function(t){return this.regex.exec(t)[1].replace("\\/","/")}},font:{regex:/font\:(.*)/,output:function(t){return this.regex.exec(t)[1]}},auto:{regex:/^auto$/},textmode:{regex:/textmode\:(.*)/,output:function(t){return this.regex.exec(t)[1]}},random:{regex:/^random$/}}},T=function(){var t=null,e=null,n=null;return function(i){var s=i.root;if(x.setup.supportsSVG){var r=!1;null==t&&(r=!0),t=d(t,s.properties.width,s.properties.height),r&&(e=h("text",w),n=function(t){return C.createTextNode(t)}(null),u(e,{x:0}),e.appendChild(n),t.appendChild(e),C.body.appendChild(t),t.style.visibility="hidden",t.style.position="absolute",t.style.top="-100%",t.style.left="-100%");var o=s.children.holderTextGroup.properties;u(e,{y:o.font.size,style:g({"font-weight":o.font.weight,"font-size":o.font.size+o.font.units,"font-family":o.font.family,"dominant-baseline":"middle"})}),n.nodeValue=o.text;var a=e.getBBox(),l=Math.ceil(a.width/(s.properties.width*x.setup.lineWrapRatio)),c=o.text.split(" "),f=o.text.match(/\\n/g);l+=null==f?0:f.length,n.nodeValue=o.text.replace(/[ ]+/g,"");var p=e.getComputedTextLength(),m=a.width-p,v=Math.round(m/Math.max(1,c.length-1)),b=[];if(l>1){n.nodeValue="";for(var y=0;y<c.length;y++)if(0!==c[y].length){n.nodeValue=_(c[y]);var S=e.getBBox();b.push({text:c[y],width:S.width})}}return{spaceWidth:v,lineCount:l,boundingBox:a,words:b}}return!1}}(),D=function(){var t=h("canvas"),e=null;return function(n){null==e&&(e=t.getContext("2d"));var i=n.root;t.width=x.dpr(i.properties.width),t.height=x.dpr(i.properties.height),e.textBaseline="middle",e.fillStyle=i.children.holderBg.properties.fill,e.fillRect(0,0,x.dpr(i.children.holderBg.width),x.dpr(i.children.holderBg.height));var s=i.children.holderTextGroup;for(var r in s.properties,e.font=s.properties.font.weight+" "+x.dpr(s.properties.font.size)+s.properties.font.units+" "+s.properties.font.family+", monospace",e.fillStyle=s.properties.fill,s.children){var o=s.children[r];for(var a in o.children){var l=o.children[a],c=x.dpr(s.x+o.x+l.x),h=x.dpr(s.y+o.y+l.y+s.properties.leading/2);e.fillText(l.properties.text,c,h)}}return t.toDataURL("image/png")}}(),E=function(){if(e.XMLSerializer){var t=d(null,0,0),n=h("rect",w);return t.appendChild(n),function(e,i){var s=e.root;d(t,s.properties.width,s.properties.height);for(var r=t.querySelectorAll("g"),o=0;o<r.length;o++)r[o].parentNode.removeChild(r[o]);u(n,{width:s.children.holderBg.width,height:s.children.holderBg.height,fill:s.children.holderBg.properties.fill});var a=s.children.holderTextGroup,l=a.properties,c=h("g",w);for(var p in t.appendChild(c),a.children){var m=a.children[p];for(var v in m.children){var b=m.children[v],y=a.x+m.x+b.x,_=a.y+m.y+b.y+a.properties.leading/2,S=h("text",w),x=C.createTextNode(null);u(S,{x:y,y:_,style:g({fill:l.fill,"font-weight":l.font.weight,"font-family":l.font.family+", monospace","font-size":l.font.size+l.font.units,"dominant-baseline":"central"})}),x.nodeValue=b.properties.text,S.appendChild(x),c.appendChild(S)}}return"data:image/svg+xml;base64,"+btoa(unescape(encodeURIComponent(f(t,i))))}}}(),I=function(t){var e=1,n=augment.defclass({constructor:function(t){e++,this.parent=null,this.children={},this.id=e,this.name="n"+e,null!=t&&(this.name=t),this.x=0,this.y=0,this.z=0,this.width=0,this.height=0},resize:function(t,e){null!=t&&(this.width=t),null!=e&&(this.height=e)},moveTo:function(t,e,n){this.x=null!=t?t:this.x,this.y=null!=e?e:this.y,this.z=null!=n?n:this.z},add:function(t){var e=t.name;if(null!=this.children[e])throw"SceneGraph: child with that name already exists: "+e;this.children[e]=t,t.parent=this}}),i=augment(n,function(e){this.constructor=function(){e.constructor.call(this,"root"),this.properties=t}}),s=augment(n,function(t){function e(e,n){if(t.constructor.call(this,e),this.properties={fill:"#000"},null!=n)!function(t,e){for(var n in e)t[n]=e[n]}(this.properties,n);else if(null!=e&&"string"!=typeof e)throw"SceneGraph: invalid node name"}this.Group=augment.extend(this,{constructor:e,type:"group"}),this.Rect=augment.extend(this,{constructor:e,type:"rect"}),this.Text=augment.extend(this,{constructor:function(t){e.call(this),this.properties.text=t},type:"text"})}),r=new i;return this.Shape=s,this.root=r,this};for(var A in x.flags)x.flags.hasOwnProperty(A)&&(x.flags[A].match=function(t){return t.match(this.regex)});x.setup={renderer:"html",debounce:100,ratio:1,supportsCanvas:!1,supportsSVG:!1,lineWrapRatio:.9,renderers:["html","canvas","svg"]},x.dpr=function(t){return t*x.setup.ratio},x.vars={preempted:!1,resizableImages:[],debounceTimer:null,cache:{}},function(){var t=1,n=1,i=h("canvas"),s=null;i.getContext&&-1!=i.toDataURL("image/png").indexOf("data:image/png")&&(x.setup.renderer="canvas",s=i.getContext("2d"),x.setup.supportsCanvas=!0),x.setup.supportsCanvas&&(t=e.devicePixelRatio||1,n=s.webkitBackingStorePixelRatio||s.mozBackingStorePixelRatio||s.msBackingStorePixelRatio||s.oBackingStorePixelRatio||s.backingStorePixelRatio||1),x.setup.ratio=t/n,C.createElementNS&&C.createElementNS(w,"svg").createSVGRect&&(x.setup.renderer="svg",x.setup.supportsSVG=!0)}(),function(t,e,n){"function"==typeof define&&define.amd?define(t):n[e]=t}(S,"Holder",e),e.onDomReady&&e.onDomReady(function(){x.vars.preempted||S.run(),e.addEventListener?(e.addEventListener("resize",m,!1),e.addEventListener("orientationchange",m,!1)):e.attachEvent("onresize",m),"object"==typeof e.Turbolinks&&e.document.addEventListener("page:change",function(){S.run()})})}(0,this),function(t){var e=function(){"use strict";return{isMsie:function(){return!!/(msie|trident)/i.test(navigator.userAgent)&&navigator.userAgent.match(/(msie |rv:)(\d+(.\d+)?)/i)[2]},isBlankString:function(t){return!t||/^\s*$/.test(t)},escapeRegExChars:function(t){return t.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")},isString:function(t){return"string"==typeof t},isNumber:function(t){return"number"==typeof t},isArray:t.isArray,isFunction:t.isFunction,isObject:t.isPlainObject,isUndefined:function(t){return void 0===t},toStr:function(t){return e.isUndefined(t)||null===t?"":t+""},bind:t.proxy,each:function(e,n){t.each(e,function(t,e){return n(e,t)})},map:t.map,filter:t.grep,every:function(e,n){var i=!0;return e?(t.each(e,function(t,s){if(!(i=n.call(null,s,t,e)))return!1}),!!i):i},some:function(e,n){var i=!1;return e?(t.each(e,function(t,s){if(i=n.call(null,s,t,e))return!1}),!!i):i},mixin:t.extend,getUniqueId:(n=0,function(){return n++}),templatify:function(e){return t.isFunction(e)?e:function(){return String(e)}},defer:function(t){setTimeout(t,0)},debounce:function(t,e,n){var i,s;return function(){var r,o,a=this,l=arguments;return r=function(){i=null,n||(s=t.apply(a,l))},o=n&&!i,clearTimeout(i),i=setTimeout(r,e),o&&(s=t.apply(a,l)),s}},throttle:function(t,e){var n,i,s,r,o,a;return o=0,a=function(){o=new Date,s=null,r=t.apply(n,i)},function(){var l=new Date,c=e-(l-o);return n=this,i=arguments,c<=0?(clearTimeout(s),s=null,o=l,r=t.apply(n,i)):s||(s=setTimeout(a,c)),r}},noop:function(){}};var n}(),n="0.10.5",i=function(){"use strict";return{nonword:n,whitespace:t,obj:{nonword:i(n),whitespace:i(t)}};function t(t){return(t=e.toStr(t))?t.split(/\s+/):[]}function n(t){return(t=e.toStr(t))?t.split(/\W+/):[]}function i(t){return function(){var n=[].slice.call(arguments,0);return function(i){var s=[];return e.each(n,function(n){s=s.concat(t(e.toStr(i[n])))}),s}}}}(),s=function(){"use strict";function n(n){this.maxSize=e.isNumber(n)?n:100,this.reset(),this.maxSize<=0&&(this.set=this.get=t.noop)}function i(){this.head=this.tail=null}return e.mixin(n.prototype,{set:function(t,e){var n,i=this.list.tail;this.size>=this.maxSize&&(this.list.remove(i),delete this.hash[i.key]),(n=this.hash[t])?(n.val=e,this.list.moveToFront(n)):(n=new function(t,e){this.key=t,this.val=e,this.prev=this.next=null}(t,e),this.list.add(n),this.hash[t]=n,this.size++)},get:function(t){var e=this.hash[t];if(e)return this.list.moveToFront(e),e.val},reset:function(){this.size=0,this.hash={},this.list=new i}}),e.mixin(i.prototype,{add:function(t){this.head&&(t.next=this.head,this.head.prev=t),this.head=t,this.tail=this.tail||t},remove:function(t){t.prev?t.prev.next=t.next:this.head=t.next,t.next?t.next.prev=t.prev:this.tail=t.prev},moveToFront:function(t){this.remove(t),this.add(t)}}),n}(),r=function(){"use strict";var t,n;try{(t=window.localStorage).setItem("~~~","!"),t.removeItem("~~~")}catch(e){t=null}function i(t){this.prefix=["__",t,"__"].join(""),this.ttlKey="__ttl__",this.keyMatcher=new RegExp("^"+e.escapeRegExChars(this.prefix))}return n=t&&window.JSON?{_prefix:function(t){return this.prefix+t},_ttlKey:function(t){return this._prefix(t)+this.ttlKey},get:function(e){return this.isExpired(e)&&this.remove(e),o(t.getItem(this._prefix(e)))},set:function(n,i,o){return e.isNumber(o)?t.setItem(this._ttlKey(n),r(s()+o)):t.removeItem(this._ttlKey(n)),t.setItem(this._prefix(n),r(i))},remove:function(e){return t.removeItem(this._ttlKey(e)),t.removeItem(this._prefix(e)),this},clear:function(){var e,n,i=[],s=t.length;for(e=0;e<s;e++)(n=t.key(e)).match(this.keyMatcher)&&i.push(n.replace(this.keyMatcher,""));for(e=i.length;e--;)this.remove(i[e]);return this},isExpired:function(n){var i=o(t.getItem(this._ttlKey(n)));return!!(e.isNumber(i)&&s()>i)}}:{get:e.noop,set:e.noop,remove:e.noop,clear:e.noop,isExpired:e.noop},e.mixin(i.prototype,n),i;function s(){return(new Date).getTime()}function r(t){return JSON.stringify(e.isUndefined(t)?null:t)}function o(t){return JSON.parse(t)}}(),o=function(){"use strict";var n=0,i={},r=6,o=new s(10);function a(n){var i;n=n||{},this.cancelled=!1,this.lastUrl=null,this._send=n.transport?(i=n.transport,function(n,s){var r=t.Deferred();return i(n,s,function(t){e.defer(function(){r.resolve(t)})},function(t){e.defer(function(){r.reject(t)})}),r}):t.ajax,this._get=n.rateLimiter?n.rateLimiter(this._get):this._get,this._cache=!1===n.cache?new s(0):o}return a.setMaxPendingRequests=function(t){r=t},a.resetCache=function(){o.reset()},e.mixin(a.prototype,{_get:function(t,e,s){var o,a=this;function l(e){s&&s(null,e),a._cache.set(t,e)}function c(){s&&s(!0)}this.cancelled||t!==this.lastUrl||((o=i[t])?o.done(l).fail(c):n<r?(n++,i[t]=this._send(t,e).done(l).fail(c).always(function(){n--,delete i[t],a.onDeckRequestArgs&&(a._get.apply(a,a.onDeckRequestArgs),a.onDeckRequestArgs=null)})):this.onDeckRequestArgs=[].slice.call(arguments,0))},get:function(t,n,i){var s;return e.isFunction(n)&&(i=n,n={}),this.cancelled=!1,this.lastUrl=t,(s=this._cache.get(t))?e.defer(function(){i&&i(null,s)}):this._get(t,n,i),!!s},cancel:function(){this.cancelled=!0}}),a}(),a=function(){"use strict";function n(e){(e=e||{}).datumTokenizer&&e.queryTokenizer||t.error("datumTokenizer and queryTokenizer are both required"),this.datumTokenizer=e.datumTokenizer,this.queryTokenizer=e.queryTokenizer,this.reset()}return e.mixin(n.prototype,{bootstrap:function(t){this.datums=t.datums,this.trie=t.trie},add:function(t){var n=this;t=e.isArray(t)?t:[t],e.each(t,function(t){var s,r;s=n.datums.push(t)-1,r=i(n.datumTokenizer(t)),e.each(r,function(t){var e,i,r;for(e=n.trie,i=t.split("");r=i.shift();)(e=e.children[r]||(e.children[r]={ids:[],children:{}})).ids.push(s)})})},get:function(t){var n,s,r=this;return n=i(this.queryTokenizer(t)),e.each(n,function(t){var e,n,i,o;if(s&&0===s.length)return!1;for(e=r.trie,n=t.split("");e&&(i=n.shift());)e=e.children[i];if(!e||0!==n.length)return s=[],!1;o=e.ids.slice(0),s=s?function(t,e){var n=0,i=0,s=[];t=t.sort(a),e=e.sort(a);var r=t.length,o=e.length;for(;n<r&&i<o;)t[n]<e[i]?n++:t[n]>e[i]?i++:(s.push(t[n]),n++,i++);return s;function a(t,e){return t-e}}(s,o):o}),s?e.map(function(t){for(var e={},n=[],i=0,s=t.length;i<s;i++)e[t[i]]||(e[t[i]]=!0,n.push(t[i]));return n}(s),function(t){return r.datums[t]}):[]},reset:function(){this.datums=[],this.trie={ids:[],children:{}}},serialize:function(){return{datums:this.datums,trie:this.trie}}}),n;function i(t){return t=e.filter(t,function(t){return!!t}),t=e.map(t,function(t){return t.toLowerCase()})}}(),l=function(){"use strict";return{local:function(t){return t.local||null},prefetch:function(i){var s,r;r={url:null,thumbprint:"",ttl:864e5,filter:null,ajax:{}},(s=i.prefetch||null)&&(s=e.isString(s)?{url:s}:s,(s=e.mixin(r,s)).thumbprint=n+s.thumbprint,s.ajax.type=s.ajax.type||"GET",s.ajax.dataType=s.ajax.dataType||"json",!s.url&&t.error("prefetch requires url to be set"));return s},remote:function(n){var i,s;s={url:null,cache:!0,wildcard:"%QUERY",replace:null,rateLimitBy:"debounce",rateLimitWait:300,send:null,filter:null,ajax:{}},(i=n.remote||null)&&(i=e.isString(i)?{url:i}:i,(i=e.mixin(s,i)).rateLimiter=/^throttle$/i.test(i.rateLimitBy)?(r=i.rateLimitWait,function(t){return e.throttle(t,r)}):function(t){return function(n){return e.debounce(n,t)}}(i.rateLimitWait),i.ajax.type=i.ajax.type||"GET",i.ajax.dataType=i.ajax.dataType||"json",delete i.rateLimitBy,delete i.rateLimitWait,!i.url&&t.error("remote requires url to be set"));var r;return i}}}();!function(n){"use strict";var s,c;function h(n){n&&(n.local||n.prefetch||n.remote)||t.error("one of local, prefetch, or remote is required"),this.limit=n.limit||5,this.sorter=function(t){return e.isFunction(t)?function(e){return e.sort(t)}:function(t){return t}}(n.sorter),this.dupDetector=n.dupDetector||u,this.local=l.local(n),this.prefetch=l.prefetch(n),this.remote=l.remote(n),this.cacheKey=this.prefetch?this.prefetch.cacheKey||this.prefetch.url:null,this.index=new a({datumTokenizer:n.datumTokenizer,queryTokenizer:n.queryTokenizer}),this.storage=this.cacheKey?new r(this.cacheKey):null}return s=n.Bloodhound,c={data:"data",protocol:"protocol",thumbprint:"thumbprint"},n.Bloodhound=h,h.noConflict=function(){return n.Bloodhound=s,h},h.tokenizers=i,e.mixin(h.prototype,{_loadPrefetch:function(e){var n,i,s=this;return(n=this._readFromStorage(e.thumbprint))?(this.index.bootstrap(n),i=t.Deferred().resolve()):i=t.ajax(e.url,e.ajax).done(function(t){s.clear(),s.add(e.filter?e.filter(t):t),s._saveToStorage(s.index.serialize(),e.thumbprint,e.ttl)}),i},_getFromRemote:function(t,e){var n,i,s=this;if(this.transport)return t=t||"",i=encodeURIComponent(t),n=this.remote.replace?this.remote.replace(this.remote.url,t):this.remote.url.replace(this.remote.wildcard,i),this.transport.get(n,this.remote.ajax,function(t,n){e(t?[]:s.remote.filter?s.remote.filter(n):n)})},_cancelLastRemoteRequest:function(){this.transport&&this.transport.cancel()},_saveToStorage:function(t,e,n){this.storage&&(this.storage.set(c.data,t,n),this.storage.set(c.protocol,location.protocol,n),this.storage.set(c.thumbprint,e,n))},_readFromStorage:function(t){var e,n={};return this.storage&&(n.data=this.storage.get(c.data),n.protocol=this.storage.get(c.protocol),n.thumbprint=this.storage.get(c.thumbprint)),e=n.thumbprint!==t||n.protocol!==location.protocol,n.data&&!e?n.data:null},_initialize:function(){var n,i=this,s=this.local;return n=this.prefetch?this._loadPrefetch(this.prefetch):t.Deferred().resolve(),s&&n.done(function(){i.add(e.isFunction(s)?s():s)}),this.transport=this.remote?new o(this.remote):null,this.initPromise=n.promise()},initialize:function(t){return!this.initPromise||t?this._initialize():this.initPromise},add:function(t){this.index.add(t)},get:function(t,n){var i=this,s=[],r=!1;s=this.index.get(t),(s=this.sorter(s).slice(0,this.limit)).length<this.limit?r=this._getFromRemote(t,function(t){var r=s.slice(0);e.each(t,function(t){return!e.some(r,function(e){return i.dupDetector(t,e)})&&r.push(t),r.length<i.limit}),n&&n(i.sorter(r))}):this._cancelLastRemoteRequest(),r||(s.length>0||!this.transport)&&n&&n(s)},clear:function(){this.index.reset()},clearPrefetchCache:function(){this.storage&&this.storage.clear()},clearRemoteCache:function(){this.transport&&o.resetCache()},ttAdapter:function(){return e.bind(this.get,this)}}),h;function u(){return!1}}(this);var c={wrapper:'<span class="twitter-typeahead"></span>',dropdown:'<span class="tt-dropdown-menu"></span>',dataset:'<div class="tt-dataset-%CLASS%"></div>',suggestions:'<span class="tt-suggestions"></span>',suggestion:'<div class="tt-suggestion"></div>'},h=function(){"use strict";var t={wrapper:{position:"relative",display:"inline-block"},hint:{position:"absolute",top:"0",left:"0",borderColor:"transparent",boxShadow:"none",opacity:"1"},input:{position:"relative",verticalAlign:"top",backgroundColor:"transparent"},inputWithNoHint:{position:"relative",verticalAlign:"top"},dropdown:{position:"absolute",top:"100%",left:"0",zIndex:"100",display:"none"},suggestions:{display:"block"},suggestion:{whiteSpace:"nowrap",cursor:"pointer"},suggestionChild:{whiteSpace:"normal"},ltr:{left:"0",right:"auto"},rtl:{left:"auto",right:" 0"}};return e.isMsie()&&e.mixin(t.input,{backgroundImage:"url(data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7)"}),e.isMsie()&&e.isMsie()<=7&&e.mixin(t.input,{marginTop:"-1px"}),t}(),u=function(){"use strict";function n(e){e&&e.el||t.error("EventBus initialized without el"),this.$el=t(e.el)}return e.mixin(n.prototype,{trigger:function(t){var e=[].slice.call(arguments,1);this.$el.trigger("typeahead:"+t,e)}}),n}(),d=function(){"use strict";var t=/\s+/,e=function(){var t;t=window.setImmediate?function(t){setImmediate(function(){t()})}:function(t){setTimeout(function(){t()},0)};return t}();return{onSync:function(t,e,i){return n.call(this,"sync",t,e,i)},onAsync:function(t,e,i){return n.call(this,"async",t,e,i)},off:function(e){var n;if(!this._callbacks)return this;e=e.split(t);for(;n=e.shift();)delete this._callbacks[n];return this},trigger:function(n){var s,r,o,a,l;if(!this._callbacks)return this;n=n.split(t),o=[].slice.call(arguments,1);for(;(s=n.shift())&&(r=this._callbacks[s]);)a=i(r.sync,this,[s].concat(o)),l=i(r.async,this,[s].concat(o)),a()&&e(l);return this}};function n(e,n,i,s){var r;if(!i)return this;for(n=n.split(t),i=s?function(t,e){return t.bind?t.bind(e):function(){t.apply(e,[].slice.call(arguments,0))}}(i,s):i,this._callbacks=this._callbacks||{};r=n.shift();)this._callbacks[r]=this._callbacks[r]||{sync:[],async:[]},this._callbacks[r][e].push(i);return this}function i(t,e,n){return function(){for(var i,s=0,r=t.length;!i&&s<r;s+=1)i=!1===t[s].apply(e,n);return!i}}}(),f=function(t){"use strict";var n={node:null,pattern:null,tagName:"strong",className:null,wordsOnly:!1,caseSensitive:!1};return function(i){var s;(i=e.mixin({},n,i)).node&&i.pattern&&(i.pattern=e.isArray(i.pattern)?i.pattern:[i.pattern],s=function(t,n,i){for(var s,r=[],o=0,a=t.length;o<a;o++)r.push(e.escapeRegExChars(t[o]));return s=i?"\\b("+r.join("|")+")\\b":"("+r.join("|")+")",n?new RegExp(s):new RegExp(s,"i")}(i.pattern,i.caseSensitive,i.wordsOnly),function t(e,n){var i;for(var s=0;s<e.childNodes.length;s++)3===(i=e.childNodes[s]).nodeType?s+=n(i)?1:0:t(i,n)}(i.node,function(e){var n,r,o;(n=s.exec(e.data))&&(o=t.createElement(i.tagName),i.className&&(o.className=i.className),(r=e.splitText(n.index)).splitText(n[0].length),o.appendChild(r.cloneNode(!0)),e.parentNode.replaceChild(o,r));return!!n}))}}(window.document),p=function(){"use strict";var n;function i(i){var s,r,o,a,l,c=this;(i=i||{}).input||t.error("input is missing"),s=e.bind(this._onBlur,this),r=e.bind(this._onFocus,this),o=e.bind(this._onKeydown,this),a=e.bind(this._onInput,this),this.$hint=t(i.hint),this.$input=t(i.input).on("blur.tt",s).on("focus.tt",r).on("keydown.tt",o),0===this.$hint.length&&(this.setHint=this.getHint=this.clearHint=this.clearHintIfInvalid=e.noop),e.isMsie()?this.$input.on("keydown.tt keypress.tt cut.tt paste.tt",function(t){n[t.which||t.keyCode]||e.defer(e.bind(c._onInput,c,t))}):this.$input.on("input.tt",a),this.query=this.$input.val(),this.$overflowHelper=(l=this.$input,t('<pre aria-hidden="true"></pre>').css({position:"absolute",visibility:"hidden",whiteSpace:"pre",fontFamily:l.css("font-family"),fontSize:l.css("font-size"),fontStyle:l.css("font-style"),fontVariant:l.css("font-variant"),fontWeight:l.css("font-weight"),wordSpacing:l.css("word-spacing"),letterSpacing:l.css("letter-spacing"),textIndent:l.css("text-indent"),textRendering:l.css("text-rendering"),textTransform:l.css("text-transform")}).insertAfter(l))}return n={9:"tab",27:"esc",37:"left",39:"right",13:"enter",38:"up",40:"down"},i.normalizeQuery=function(t){return(t||"").replace(/^\s*/g,"").replace(/\s{2,}/g," ")},e.mixin(i.prototype,d,{_onBlur:function(){this.resetInputValue(),this.trigger("blurred")},_onFocus:function(){this.trigger("focused")},_onKeydown:function(t){var e=n[t.which||t.keyCode];this._managePreventDefault(e,t),e&&this._shouldTrigger(e,t)&&this.trigger(e+"Keyed",t)},_onInput:function(){this._checkInputValue()},_managePreventDefault:function(t,e){var n,i,r;switch(t){case"tab":i=this.getHint(),r=this.getInputValue(),n=i&&i!==r&&!s(e);break;case"up":case"down":n=!s(e);break;default:n=!1}n&&e.preventDefault()},_shouldTrigger:function(t,e){var n;switch(t){case"tab":n=!s(e);break;default:n=!0}return n},_checkInputValue:function(){var t,e,n,s,r;t=this.getInputValue(),s=t,r=this.query,n=!!(e=i.normalizeQuery(s)===i.normalizeQuery(r))&&this.query.length!==t.length,this.query=t,e?n&&this.trigger("whitespaceChanged",this.query):this.trigger("queryChanged",this.query)},focus:function(){this.$input.focus()},blur:function(){this.$input.blur()},getQuery:function(){return this.query},setQuery:function(t){this.query=t},getInputValue:function(){return this.$input.val()},setInputValue:function(t,e){this.$input.val(t),e?this.clearHint():this._checkInputValue()},resetInputValue:function(){this.setInputValue(this.query,!0)},getHint:function(){return this.$hint.val()},setHint:function(t){this.$hint.val(t)},clearHint:function(){this.setHint("")},clearHintIfInvalid:function(){var t,e,n;n=(t=this.getInputValue())!==(e=this.getHint())&&0===e.indexOf(t),!(""!==t&&n&&!this.hasOverflow())&&this.clearHint()},getLanguageDirection:function(){return(this.$input.css("direction")||"ltr").toLowerCase()},hasOverflow:function(){var t=this.$input.width()-2;return this.$overflowHelper.text(this.getInputValue()),this.$overflowHelper.width()>=t},isCursorAtEnd:function(){var t,n,i;return t=this.$input.val().length,n=this.$input[0].selectionStart,e.isNumber(n)?n===t:!document.selection||((i=document.selection.createRange()).moveStart("character",-t),t===i.text.length)},destroy:function(){this.$hint.off(".tt"),this.$input.off(".tt"),this.$hint=this.$input=this.$overflowHelper=null}}),i;function s(t){return t.altKey||t.ctrlKey||t.metaKey||t.shiftKey}}(),g=function(){"use strict";var n="ttDataset",i="ttValue",s="ttDatum";function r(n){var i;(n=n||{}).templates=n.templates||{},n.source||t.error("missing source"),n.name&&(i=n.name,!/^[_a-zA-Z0-9-]+$/.test(i))&&t.error("invalid dataset name: "+n.name),this.query=null,this.highlight=!!n.highlight,this.name=n.name||e.getUniqueId(),this.source=n.source,this.displayFn=function(t){return t=t||"value",e.isFunction(t)?t:function(e){return e[t]}}(n.display||n.displayKey),this.templates=function(t,n){return{empty:t.empty&&e.templatify(t.empty),header:t.header&&e.templatify(t.header),footer:t.footer&&e.templatify(t.footer),suggestion:t.suggestion||function(t){return"<p>"+n(t)+"</p>"}}}(n.templates,this.displayFn),this.$el=t(c.dataset.replace("%CLASS%",this.name))}return r.extractDatasetName=function(e){return t(e).data(n)},r.extractValue=function(e){return t(e).data(i)},r.extractDatum=function(e){return t(e).data(s)},e.mixin(r.prototype,d,{_render:function(r,o){if(this.$el){var a,l=this;this.$el.empty(),!(a=o&&o.length)&&this.templates.empty?this.$el.html(l.templates.empty({query:r,isEmpty:!0})).prepend(l.templates.header?u():null).append(l.templates.footer?d():null):a&&this.$el.html(function(){var a,u;return a=t(c.suggestions).css(h.suggestions),u=e.map(o,function(e){var r;return(r=t(c.suggestion).append(l.templates.suggestion(e)).data(n,l.name).data(i,l.displayFn(e)).data(s,e)).children().each(function(){t(this).css(h.suggestionChild)}),r}),a.append.apply(a,u),l.highlight&&f({className:"tt-highlight",node:a[0],pattern:r}),a}()).prepend(l.templates.header?u():null).append(l.templates.footer?d():null),this.trigger("rendered")}function u(){return l.templates.header({query:r,isEmpty:!a})}function d(){return l.templates.footer({query:r,isEmpty:!a})}},getRoot:function(){return this.$el},update:function(t){var e=this;this.query=t,this.canceled=!1,this.source(t,function(n){e.canceled||t!==e.query||e._render(t,n)})},cancel:function(){this.canceled=!0},clear:function(){this.cancel(),this.$el.empty(),this.trigger("rendered")},isEmpty:function(){return this.$el.is(":empty")},destroy:function(){this.$el=null}}),r}(),m=function(){"use strict";function n(n){var s,r,o,a=this;(n=n||{}).menu||t.error("menu is required"),this.isOpen=!1,this.isEmpty=!0,this.datasets=e.map(n.datasets,i),s=e.bind(this._onSuggestionClick,this),r=e.bind(this._onSuggestionMouseEnter,this),o=e.bind(this._onSuggestionMouseLeave,this),this.$menu=t(n.menu).on("click.tt",".tt-suggestion",s).on("mouseenter.tt",".tt-suggestion",r).on("mouseleave.tt",".tt-suggestion",o),e.each(this.datasets,function(t){a.$menu.append(t.getRoot()),t.onSync("rendered",a._onRendered,a)})}return e.mixin(n.prototype,d,{_onSuggestionClick:function(e){this.trigger("suggestionClicked",t(e.currentTarget))},_onSuggestionMouseEnter:function(e){this._removeCursor(),this._setCursor(t(e.currentTarget),!0)},_onSuggestionMouseLeave:function(){this._removeCursor()},_onRendered:function(){this.isEmpty=e.every(this.datasets,function(t){return t.isEmpty()}),this.isEmpty?this._hide():this.isOpen&&this._show(),this.trigger("datasetRendered")},_hide:function(){this.$menu.hide()},_show:function(){this.$menu.css("display","block")},_getSuggestions:function(){return this.$menu.find(".tt-suggestion")},_getCursor:function(){return this.$menu.find(".tt-cursor").first()},_setCursor:function(t,e){t.first().addClass("tt-cursor"),!e&&this.trigger("cursorMoved")},_removeCursor:function(){this._getCursor().removeClass("tt-cursor")},_moveCursor:function(t){var e,n,i,s;this.isOpen&&(n=this._getCursor(),e=this._getSuggestions(),this._removeCursor(),-1!==(i=((i=e.index(n)+t)+1)%(e.length+1)-1)?(i<-1&&(i=e.length-1),this._setCursor(s=e.eq(i)),this._ensureVisible(s)):this.trigger("cursorRemoved"))},_ensureVisible:function(t){var e,n,i,s;n=(e=t.position().top)+t.outerHeight(!0),i=this.$menu.scrollTop(),s=this.$menu.height()+parseInt(this.$menu.css("paddingTop"),10)+parseInt(this.$menu.css("paddingBottom"),10),e<0?this.$menu.scrollTop(i+e):s<n&&this.$menu.scrollTop(i+(n-s))},close:function(){this.isOpen&&(this.isOpen=!1,this._removeCursor(),this._hide(),this.trigger("closed"))},open:function(){this.isOpen||(this.isOpen=!0,!this.isEmpty&&this._show(),this.trigger("opened"))},setLanguageDirection:function(t){this.$menu.css("ltr"===t?h.ltr:h.rtl)},moveCursorUp:function(){this._moveCursor(-1)},moveCursorDown:function(){this._moveCursor(1)},getDatumForSuggestion:function(t){var e=null;return t.length&&(e={raw:g.extractDatum(t),value:g.extractValue(t),datasetName:g.extractDatasetName(t)}),e},getDatumForCursor:function(){return this.getDatumForSuggestion(this._getCursor().first())},getDatumForTopSuggestion:function(){return this.getDatumForSuggestion(this._getSuggestions().first())},update:function(t){e.each(this.datasets,function(e){e.update(t)})},empty:function(){e.each(this.datasets,function(t){t.clear()}),this.isEmpty=!0},isVisible:function(){return this.isOpen&&!this.isEmpty},destroy:function(){this.$menu.off(".tt"),this.$menu=null,e.each(this.datasets,function(t){t.destroy()})}}),n;function i(t){return new g(t)}}(),v=function(){"use strict";var n="ttAttrs";function i(i){var s,r,o;(i=i||{}).input||t.error("missing input"),this.isActivated=!1,this.autoselect=!!i.autoselect,this.minLength=e.isNumber(i.minLength)?i.minLength:1,this.$node=function(e,i){var s,r,o,a;s=t(e),r=t(c.wrapper).css(h.wrapper),o=t(c.dropdown).css(h.dropdown),(a=s.clone().css(h.hint).css((l=s,{backgroundAttachment:l.css("background-attachment"),backgroundClip:l.css("background-clip"),backgroundColor:l.css("background-color"),backgroundImage:l.css("background-image"),backgroundOrigin:l.css("background-origin"),backgroundPosition:l.css("background-position"),backgroundRepeat:l.css("background-repeat"),backgroundSize:l.css("background-size")}))).val("").removeData().addClass("tt-hint").removeAttr("id name placeholder required").prop("readonly",!0).attr({autocomplete:"off",spellcheck:"false",tabindex:-1}),s.data(n,{dir:s.attr("dir"),autocomplete:s.attr("autocomplete"),spellcheck:s.attr("spellcheck"),style:s.attr("style")}),s.addClass("tt-input").attr({autocomplete:"off",spellcheck:!1}).css(i?h.input:h.inputWithNoHint);var l;try{!s.attr("dir")&&s.attr("dir","auto")}catch(t){}return s.wrap(r).parent().prepend(i?a:null).append(o)}(i.input,i.withHint),s=this.$node.find(".tt-dropdown-menu"),r=this.$node.find(".tt-input"),o=this.$node.find(".tt-hint"),r.on("blur.tt",function(t){var n,i,o;n=document.activeElement,i=s.is(n),o=s.has(n).length>0,e.isMsie()&&(i||o)&&(t.preventDefault(),t.stopImmediatePropagation(),e.defer(function(){r.focus()}))}),s.on("mousedown.tt",function(t){t.preventDefault()}),this.eventBus=i.eventBus||new u({el:r}),this.dropdown=new m({menu:s,datasets:i.datasets}).onSync("suggestionClicked",this._onSuggestionClicked,this).onSync("cursorMoved",this._onCursorMoved,this).onSync("cursorRemoved",this._onCursorRemoved,this).onSync("opened",this._onOpened,this).onSync("closed",this._onClosed,this).onAsync("datasetRendered",this._onDatasetRendered,this),this.input=new p({input:r,hint:o}).onSync("focused",this._onFocused,this).onSync("blurred",this._onBlurred,this).onSync("enterKeyed",this._onEnterKeyed,this).onSync("tabKeyed",this._onTabKeyed,this).onSync("escKeyed",this._onEscKeyed,this).onSync("upKeyed",this._onUpKeyed,this).onSync("downKeyed",this._onDownKeyed,this).onSync("leftKeyed",this._onLeftKeyed,this).onSync("rightKeyed",this._onRightKeyed,this).onSync("queryChanged",this._onQueryChanged,this).onSync("whitespaceChanged",this._onWhitespaceChanged,this),this._setLanguageDirection()}return e.mixin(i.prototype,{_onSuggestionClicked:function(t,e){var n;(n=this.dropdown.getDatumForSuggestion(e))&&this._select(n)},_onCursorMoved:function(){var t=this.dropdown.getDatumForCursor();this.input.setInputValue(t.value,!0),this.eventBus.trigger("cursorchanged",t.raw,t.datasetName)},_onCursorRemoved:function(){this.input.resetInputValue(),this._updateHint()},_onDatasetRendered:function(){this._updateHint()},_onOpened:function(){this._updateHint(),this.eventBus.trigger("opened")},_onClosed:function(){this.input.clearHint(),this.eventBus.trigger("closed")},_onFocused:function(){this.isActivated=!0,this.dropdown.open()},_onBlurred:function(){this.isActivated=!1,this.dropdown.empty(),this.dropdown.close()},_onEnterKeyed:function(t,e){var n,i;n=this.dropdown.getDatumForCursor(),i=this.dropdown.getDatumForTopSuggestion(),n?(this._select(n),e.preventDefault()):this.autoselect&&i&&(this._select(i),e.preventDefault())},_onTabKeyed:function(t,e){var n;(n=this.dropdown.getDatumForCursor())?(this._select(n),e.preventDefault()):this._autocomplete(!0)},_onEscKeyed:function(){this.dropdown.close(),this.input.resetInputValue()},_onUpKeyed:function(){var t=this.input.getQuery();this.dropdown.isEmpty&&t.length>=this.minLength?this.dropdown.update(t):this.dropdown.moveCursorUp(),this.dropdown.open()},_onDownKeyed:function(){var t=this.input.getQuery();this.dropdown.isEmpty&&t.length>=this.minLength?this.dropdown.update(t):this.dropdown.moveCursorDown(),this.dropdown.open()},_onLeftKeyed:function(){"rtl"===this.dir&&this._autocomplete()},_onRightKeyed:function(){"ltr"===this.dir&&this._autocomplete()},_onQueryChanged:function(t,e){this.input.clearHintIfInvalid(),e.length>=this.minLength?this.dropdown.update(e):this.dropdown.empty(),this.dropdown.open(),this._setLanguageDirection()},_onWhitespaceChanged:function(){this._updateHint(),this.dropdown.open()},_setLanguageDirection:function(){var t;this.dir!==(t=this.input.getLanguageDirection())&&(this.dir=t,this.$node.css("direction",t),this.dropdown.setLanguageDirection(t))},_updateHint:function(){var t,n,i,s,r;(t=this.dropdown.getDatumForTopSuggestion())&&this.dropdown.isVisible()&&!this.input.hasOverflow()?(n=this.input.getInputValue(),i=p.normalizeQuery(n),s=e.escapeRegExChars(i),(r=new RegExp("^(?:"+s+")(.+$)","i").exec(t.value))?this.input.setHint(n+r[1]):this.input.clearHint()):this.input.clearHint()},_autocomplete:function(t){var e,n,i,s;e=this.input.getHint(),n=this.input.getQuery(),i=t||this.input.isCursorAtEnd(),e&&n!==e&&i&&((s=this.dropdown.getDatumForTopSuggestion())&&this.input.setInputValue(s.value),this.eventBus.trigger("autocompleted",s.raw,s.datasetName))},_select:function(t){this.input.setQuery(t.value),this.input.setInputValue(t.value,!0),this._setLanguageDirection(),this.eventBus.trigger("selected",t.raw,t.datasetName),this.dropdown.close(),e.defer(e.bind(this.dropdown.empty,this.dropdown))},open:function(){this.dropdown.open()},close:function(){this.dropdown.close()},setVal:function(t){t=e.toStr(t),this.isActivated?this.input.setInputValue(t):(this.input.setQuery(t),this.input.setInputValue(t,!0)),this._setLanguageDirection()},getVal:function(){return this.input.getQuery()},destroy:function(){var t,i;this.input.destroy(),this.dropdown.destroy(),t=this.$node,i=t.find(".tt-input"),e.each(i.data(n),function(t,n){e.isUndefined(t)?i.removeAttr(n):i.attr(n,t)}),i.detach().removeData(n).removeClass("tt-input").insertAfter(t),t.remove(),this.$node=null}}),i}();!function(){"use strict";var n,i,s;n=t.fn.typeahead,i="ttTypeahead",s={initialize:function(n,s){return s=e.isArray(s)?s:[].slice.call(arguments,1),n=n||{},this.each(function(){var r,o=t(this);e.each(s,function(t){t.highlight=!!n.highlight}),r=new v({input:o,eventBus:new u({el:o}),withHint:!!e.isUndefined(n.hint)||!!n.hint,minLength:n.minLength,autoselect:n.autoselect,datasets:s}),o.data(i,r)})},open:function(){return this.each(function(){var e,n=t(this);(e=n.data(i))&&e.open()})},close:function(){return this.each(function(){var e,n=t(this);(e=n.data(i))&&e.close()})},val:function(e){return arguments.length?this.each(function(){var n,s=t(this);(n=s.data(i))&&n.setVal(e)}):function(t){var e,n;(e=t.data(i))&&(n=e.getVal());return n}(this.first())},destroy:function(){return this.each(function(){var e,n=t(this);(e=n.data(i))&&(e.destroy(),n.removeData(i))})}},t.fn.typeahead=function(e){var n;return s[e]&&"initialize"!==e?(n=this.filter(function(){return!!t(this).data(i)}),s[e].apply(n,[].slice.call(arguments,1))):s.initialize.apply(this,arguments)},t.fn.typeahead.noConflict=function(){return t.fn.typeahead=n,this}}()}(window.jQuery),function(t,e,n){var i,s,r="[object OperaMini]"==Object.prototype.toString.call(t.operamini),o="placeholder"in e.createElement("input")&&!r,a="placeholder"in e.createElement("textarea")&&!r,l=n.fn,c=n.valHooks,h=n.propHooks;function u(t,e){var i=n(this);if(this.value==i.attr("placeholder")&&i.hasClass("placeholder"))if(i.data("placeholder-password")){if(i=i.hide().next().show().attr("id",i.removeAttr("id").data("placeholder-id")),!0===t)return i[0].value=e;i.focus()}else this.value="",i.removeClass("placeholder"),this==f()&&this.select()}function d(){var t,e,i,s,r=n(this),o=this.id;if(""==this.value){if("password"==this.type){if(!r.data("placeholder-textinput")){try{t=r.clone().attr({type:"text"})}catch(r){t=n("<input>").attr(n.extend((e=this,i={},s=/^jQuery\d+$/,n.each(e.attributes,function(t,e){e.specified&&!s.test(e.name)&&(i[e.name]=e.value)}),i),{type:"text"}))}t.removeAttr("name").data({"placeholder-password":r,"placeholder-id":o}).bind("focus.placeholder",u),r.data({"placeholder-textinput":t,"placeholder-id":o}).before(t)}r=r.removeAttr("id").hide().prev().attr("id",o).show()}r.addClass("placeholder"),r[0].value=r.attr("placeholder")}else r.removeClass("placeholder")}function f(){try{return e.activeElement}catch(t){}}o&&a?(s=l.placeholder=function(){return this}).input=s.textarea=!0:((s=l.placeholder=function(){return this.filter((o?"textarea":":input")+"[placeholder]").not(".placeholder").bind({"focus.placeholder":u,"blur.placeholder":d}).data("placeholder-enabled",!0).trigger("blur.placeholder"),this}).input=o,s.textarea=a,i={get:function(t){var e=n(t),i=e.data("placeholder-password");return i?i[0].value:e.data("placeholder-enabled")&&e.hasClass("placeholder")?"":t.value},set:function(t,e){var i=n(t),s=i.data("placeholder-password");return s?s[0].value=e:i.data("placeholder-enabled")?(""==e?(t.value=e,t!=f()&&d.call(t)):i.hasClass("placeholder")&&u.call(t,!0,e)||(t.value=e),i):t.value=e}},o||(c.input=i,h.value=i),a||(c.textarea=i,h.value=i),n(function(){n(e).delegate("form","submit.placeholder",function(){var t=n(".placeholder",this).each(u);setTimeout(function(){t.each(d)},10)})}),n(t).bind("beforeunload.placeholder",function(){n(".placeholder").each(function(){this.value=""})}))}(this,document,jQuery),function(t){void 0===t.fn.each2&&t.extend(t.fn,{each2:function(e){for(var n=t([0]),i=-1,s=this.length;++i<s&&(n.context=n[0]=this[i])&&!1!==e.call(n[0],i,n););return this}})}(jQuery),function(t,e){"use strict";if(window.Select2===e){var n,i,s,r,o,a,l,c,h={x:0,y:0},u={TAB:9,ENTER:13,ESC:27,SPACE:32,LEFT:37,UP:38,RIGHT:39,DOWN:40,SHIFT:16,CTRL:17,ALT:18,PAGE_UP:33,PAGE_DOWN:34,HOME:36,END:35,BACKSPACE:8,DELETE:46,isArrow:function(t){switch(t=t.which?t.which:t){case u.LEFT:case u.RIGHT:case u.UP:case u.DOWN:return!0}return!1},isControl:function(t){switch(t.which){case u.SHIFT:case u.CTRL:case u.ALT:return!0}return!!t.metaKey},isFunctionKey:function(t){return(t=t.which?t.which:t)>=112&&t<=123}},d="<div class='select2-measure-scrollbar'></div>",f={"Ⓐ":"A","Ａ":"A","À":"A","Á":"A","Â":"A","Ầ":"A","Ấ":"A","Ẫ":"A","Ẩ":"A","Ã":"A","Ā":"A","Ă":"A","Ằ":"A","Ắ":"A","Ẵ":"A","Ẳ":"A","Ȧ":"A","Ǡ":"A","Ä":"A","Ǟ":"A","Ả":"A","Å":"A","Ǻ":"A","Ǎ":"A","Ȁ":"A","Ȃ":"A","Ạ":"A","Ậ":"A","Ặ":"A","Ḁ":"A","Ą":"A","Ⱥ":"A","Ɐ":"A","Ꜳ":"AA","Æ":"AE","Ǽ":"AE","Ǣ":"AE","Ꜵ":"AO","Ꜷ":"AU","Ꜹ":"AV","Ꜻ":"AV","Ꜽ":"AY","Ⓑ":"B","Ｂ":"B","Ḃ":"B","Ḅ":"B","Ḇ":"B","Ƀ":"B","Ƃ":"B","Ɓ":"B","Ⓒ":"C","Ｃ":"C","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","Ç":"C","Ḉ":"C","Ƈ":"C","Ȼ":"C","Ꜿ":"C","Ⓓ":"D","Ｄ":"D","Ḋ":"D","Ď":"D","Ḍ":"D","Ḑ":"D","Ḓ":"D","Ḏ":"D","Đ":"D","Ƌ":"D","Ɗ":"D","Ɖ":"D","Ꝺ":"D","Ǳ":"DZ","Ǆ":"DZ","ǲ":"Dz","ǅ":"Dz","Ⓔ":"E","Ｅ":"E","È":"E","É":"E","Ê":"E","Ề":"E","Ế":"E","Ễ":"E","Ể":"E","Ẽ":"E","Ē":"E","Ḕ":"E","Ḗ":"E","Ĕ":"E","Ė":"E","Ë":"E","Ẻ":"E","Ě":"E","Ȅ":"E","Ȇ":"E","Ẹ":"E","Ệ":"E","Ȩ":"E","Ḝ":"E","Ę":"E","Ḙ":"E","Ḛ":"E","Ɛ":"E","Ǝ":"E","Ⓕ":"F","Ｆ":"F","Ḟ":"F","Ƒ":"F","Ꝼ":"F","Ⓖ":"G","Ｇ":"G","Ǵ":"G","Ĝ":"G","Ḡ":"G","Ğ":"G","Ġ":"G","Ǧ":"G","Ģ":"G","Ǥ":"G","Ɠ":"G","Ꞡ":"G","Ᵹ":"G","Ꝿ":"G","Ⓗ":"H","Ｈ":"H","Ĥ":"H","Ḣ":"H","Ḧ":"H","Ȟ":"H","Ḥ":"H","Ḩ":"H","Ḫ":"H","Ħ":"H","Ⱨ":"H","Ⱶ":"H","Ɥ":"H","Ⓘ":"I","Ｉ":"I","Ì":"I","Í":"I","Î":"I","Ĩ":"I","Ī":"I","Ĭ":"I","İ":"I","Ï":"I","Ḯ":"I","Ỉ":"I","Ǐ":"I","Ȉ":"I","Ȋ":"I","Ị":"I","Į":"I","Ḭ":"I","Ɨ":"I","Ⓙ":"J","Ｊ":"J","Ĵ":"J","Ɉ":"J","Ⓚ":"K","Ｋ":"K","Ḱ":"K","Ǩ":"K","Ḳ":"K","Ķ":"K","Ḵ":"K","Ƙ":"K","Ⱪ":"K","Ꝁ":"K","Ꝃ":"K","Ꝅ":"K","Ꞣ":"K","Ⓛ":"L","Ｌ":"L","Ŀ":"L","Ĺ":"L","Ľ":"L","Ḷ":"L","Ḹ":"L","Ļ":"L","Ḽ":"L","Ḻ":"L","Ł":"L","Ƚ":"L","Ɫ":"L","Ⱡ":"L","Ꝉ":"L","Ꝇ":"L","Ꞁ":"L","Ǉ":"LJ","ǈ":"Lj","Ⓜ":"M","Ｍ":"M","Ḿ":"M","Ṁ":"M","Ṃ":"M","Ɱ":"M","Ɯ":"M","Ⓝ":"N","Ｎ":"N","Ǹ":"N","Ń":"N","Ñ":"N","Ṅ":"N","Ň":"N","Ṇ":"N","Ņ":"N","Ṋ":"N","Ṉ":"N","Ƞ":"N","Ɲ":"N","Ꞑ":"N","Ꞥ":"N","Ǌ":"NJ","ǋ":"Nj","Ⓞ":"O","Ｏ":"O","Ò":"O","Ó":"O","Ô":"O","Ồ":"O","Ố":"O","Ỗ":"O","Ổ":"O","Õ":"O","Ṍ":"O","Ȭ":"O","Ṏ":"O","Ō":"O","Ṑ":"O","Ṓ":"O","Ŏ":"O","Ȯ":"O","Ȱ":"O","Ö":"O","Ȫ":"O","Ỏ":"O","Ő":"O","Ǒ":"O","Ȍ":"O","Ȏ":"O","Ơ":"O","Ờ":"O","Ớ":"O","Ỡ":"O","Ở":"O","Ợ":"O","Ọ":"O","Ộ":"O","Ǫ":"O","Ǭ":"O","Ø":"O","Ǿ":"O","Ɔ":"O","Ɵ":"O","Ꝋ":"O","Ꝍ":"O","Ƣ":"OI","Ꝏ":"OO","Ȣ":"OU","Ⓟ":"P","Ｐ":"P","Ṕ":"P","Ṗ":"P","Ƥ":"P","Ᵽ":"P","Ꝑ":"P","Ꝓ":"P","Ꝕ":"P","Ⓠ":"Q","Ｑ":"Q","Ꝗ":"Q","Ꝙ":"Q","Ɋ":"Q","Ⓡ":"R","Ｒ":"R","Ŕ":"R","Ṙ":"R","Ř":"R","Ȑ":"R","Ȓ":"R","Ṛ":"R","Ṝ":"R","Ŗ":"R","Ṟ":"R","Ɍ":"R","Ɽ":"R","Ꝛ":"R","Ꞧ":"R","Ꞃ":"R","Ⓢ":"S","Ｓ":"S","ẞ":"S","Ś":"S","Ṥ":"S","Ŝ":"S","Ṡ":"S","Š":"S","Ṧ":"S","Ṣ":"S","Ṩ":"S","Ș":"S","Ş":"S","Ȿ":"S","Ꞩ":"S","Ꞅ":"S","Ⓣ":"T","Ｔ":"T","Ṫ":"T","Ť":"T","Ṭ":"T","Ț":"T","Ţ":"T","Ṱ":"T","Ṯ":"T","Ŧ":"T","Ƭ":"T","Ʈ":"T","Ⱦ":"T","Ꞇ":"T","Ꜩ":"TZ","Ⓤ":"U","Ｕ":"U","Ù":"U","Ú":"U","Û":"U","Ũ":"U","Ṹ":"U","Ū":"U","Ṻ":"U","Ŭ":"U","Ü":"U","Ǜ":"U","Ǘ":"U","Ǖ":"U","Ǚ":"U","Ủ":"U","Ů":"U","Ű":"U","Ǔ":"U","Ȕ":"U","Ȗ":"U","Ư":"U","Ừ":"U","Ứ":"U","Ữ":"U","Ử":"U","Ự":"U","Ụ":"U","Ṳ":"U","Ų":"U","Ṷ":"U","Ṵ":"U","Ʉ":"U","Ⓥ":"V","Ｖ":"V","Ṽ":"V","Ṿ":"V","Ʋ":"V","Ꝟ":"V","Ʌ":"V","Ꝡ":"VY","Ⓦ":"W","Ｗ":"W","Ẁ":"W","Ẃ":"W","Ŵ":"W","Ẇ":"W","Ẅ":"W","Ẉ":"W","Ⱳ":"W","Ⓧ":"X","Ｘ":"X","Ẋ":"X","Ẍ":"X","Ⓨ":"Y","Ｙ":"Y","Ỳ":"Y","Ý":"Y","Ŷ":"Y","Ỹ":"Y","Ȳ":"Y","Ẏ":"Y","Ÿ":"Y","Ỷ":"Y","Ỵ":"Y","Ƴ":"Y","Ɏ":"Y","Ỿ":"Y","Ⓩ":"Z","Ｚ":"Z","Ź":"Z","Ẑ":"Z","Ż":"Z","Ž":"Z","Ẓ":"Z","Ẕ":"Z","Ƶ":"Z","Ȥ":"Z","Ɀ":"Z","Ⱬ":"Z","Ꝣ":"Z","ⓐ":"a","ａ":"a","ẚ":"a","à":"a","á":"a","â":"a","ầ":"a","ấ":"a","ẫ":"a","ẩ":"a","ã":"a","ā":"a","ă":"a","ằ":"a","ắ":"a","ẵ":"a","ẳ":"a","ȧ":"a","ǡ":"a","ä":"a","ǟ":"a","ả":"a","å":"a","ǻ":"a","ǎ":"a","ȁ":"a","ȃ":"a","ạ":"a","ậ":"a","ặ":"a","ḁ":"a","ą":"a","ⱥ":"a","ɐ":"a","ꜳ":"aa","æ":"ae","ǽ":"ae","ǣ":"ae","ꜵ":"ao","ꜷ":"au","ꜹ":"av","ꜻ":"av","ꜽ":"ay","ⓑ":"b","ｂ":"b","ḃ":"b","ḅ":"b","ḇ":"b","ƀ":"b","ƃ":"b","ɓ":"b","ⓒ":"c","ｃ":"c","ć":"c","ĉ":"c","ċ":"c","č":"c","ç":"c","ḉ":"c","ƈ":"c","ȼ":"c","ꜿ":"c","ↄ":"c","ⓓ":"d","ｄ":"d","ḋ":"d","ď":"d","ḍ":"d","ḑ":"d","ḓ":"d","ḏ":"d","đ":"d","ƌ":"d","ɖ":"d","ɗ":"d","ꝺ":"d","ǳ":"dz","ǆ":"dz","ⓔ":"e","ｅ":"e","è":"e","é":"e","ê":"e","ề":"e","ế":"e","ễ":"e","ể":"e","ẽ":"e","ē":"e","ḕ":"e","ḗ":"e","ĕ":"e","ė":"e","ë":"e","ẻ":"e","ě":"e","ȅ":"e","ȇ":"e","ẹ":"e","ệ":"e","ȩ":"e","ḝ":"e","ę":"e","ḙ":"e","ḛ":"e","ɇ":"e","ɛ":"e","ǝ":"e","ⓕ":"f","ｆ":"f","ḟ":"f","ƒ":"f","ꝼ":"f","ⓖ":"g","ｇ":"g","ǵ":"g","ĝ":"g","ḡ":"g","ğ":"g","ġ":"g","ǧ":"g","ģ":"g","ǥ":"g","ɠ":"g","ꞡ":"g","ᵹ":"g","ꝿ":"g","ⓗ":"h","ｈ":"h","ĥ":"h","ḣ":"h","ḧ":"h","ȟ":"h","ḥ":"h","ḩ":"h","ḫ":"h","ẖ":"h","ħ":"h","ⱨ":"h","ⱶ":"h","ɥ":"h","ƕ":"hv","ⓘ":"i","ｉ":"i","ì":"i","í":"i","î":"i","ĩ":"i","ī":"i","ĭ":"i","ï":"i","ḯ":"i","ỉ":"i","ǐ":"i","ȉ":"i","ȋ":"i","ị":"i","į":"i","ḭ":"i","ɨ":"i","ı":"i","ⓙ":"j","ｊ":"j","ĵ":"j","ǰ":"j","ɉ":"j","ⓚ":"k","ｋ":"k","ḱ":"k","ǩ":"k","ḳ":"k","ķ":"k","ḵ":"k","ƙ":"k","ⱪ":"k","ꝁ":"k","ꝃ":"k","ꝅ":"k","ꞣ":"k","ⓛ":"l","ｌ":"l","ŀ":"l","ĺ":"l","ľ":"l","ḷ":"l","ḹ":"l","ļ":"l","ḽ":"l","ḻ":"l","ſ":"l","ł":"l","ƚ":"l","ɫ":"l","ⱡ":"l","ꝉ":"l","ꞁ":"l","ꝇ":"l","ǉ":"lj","ⓜ":"m","ｍ":"m","ḿ":"m","ṁ":"m","ṃ":"m","ɱ":"m","ɯ":"m","ⓝ":"n","ｎ":"n","ǹ":"n","ń":"n","ñ":"n","ṅ":"n","ň":"n","ṇ":"n","ņ":"n","ṋ":"n","ṉ":"n","ƞ":"n","ɲ":"n","ŉ":"n","ꞑ":"n","ꞥ":"n","ǌ":"nj","ⓞ":"o","ｏ":"o","ò":"o","ó":"o","ô":"o","ồ":"o","ố":"o","ỗ":"o","ổ":"o","õ":"o","ṍ":"o","ȭ":"o","ṏ":"o","ō":"o","ṑ":"o","ṓ":"o","ŏ":"o","ȯ":"o","ȱ":"o","ö":"o","ȫ":"o","ỏ":"o","ő":"o","ǒ":"o","ȍ":"o","ȏ":"o","ơ":"o","ờ":"o","ớ":"o","ỡ":"o","ở":"o","ợ":"o","ọ":"o","ộ":"o","ǫ":"o","ǭ":"o","ø":"o","ǿ":"o","ɔ":"o","ꝋ":"o","ꝍ":"o","ɵ":"o","ƣ":"oi","ȣ":"ou","ꝏ":"oo","ⓟ":"p","ｐ":"p","ṕ":"p","ṗ":"p","ƥ":"p","ᵽ":"p","ꝑ":"p","ꝓ":"p","ꝕ":"p","ⓠ":"q","ｑ":"q","ɋ":"q","ꝗ":"q","ꝙ":"q","ⓡ":"r","ｒ":"r","ŕ":"r","ṙ":"r","ř":"r","ȑ":"r","ȓ":"r","ṛ":"r","ṝ":"r","ŗ":"r","ṟ":"r","ɍ":"r","ɽ":"r","ꝛ":"r","ꞧ":"r","ꞃ":"r","ⓢ":"s","ｓ":"s","ß":"s","ś":"s","ṥ":"s","ŝ":"s","ṡ":"s","š":"s","ṧ":"s","ṣ":"s","ṩ":"s","ș":"s","ş":"s","ȿ":"s","ꞩ":"s","ꞅ":"s","ẛ":"s","ⓣ":"t","ｔ":"t","ṫ":"t","ẗ":"t","ť":"t","ṭ":"t","ț":"t","ţ":"t","ṱ":"t","ṯ":"t","ŧ":"t","ƭ":"t","ʈ":"t","ⱦ":"t","ꞇ":"t","ꜩ":"tz","ⓤ":"u","ｕ":"u","ù":"u","ú":"u","û":"u","ũ":"u","ṹ":"u","ū":"u","ṻ":"u","ŭ":"u","ü":"u","ǜ":"u","ǘ":"u","ǖ":"u","ǚ":"u","ủ":"u","ů":"u","ű":"u","ǔ":"u","ȕ":"u","ȗ":"u","ư":"u","ừ":"u","ứ":"u","ữ":"u","ử":"u","ự":"u","ụ":"u","ṳ":"u","ų":"u","ṷ":"u","ṵ":"u","ʉ":"u","ⓥ":"v","ｖ":"v","ṽ":"v","ṿ":"v","ʋ":"v","ꝟ":"v","ʌ":"v","ꝡ":"vy","ⓦ":"w","ｗ":"w","ẁ":"w","ẃ":"w","ŵ":"w","ẇ":"w","ẅ":"w","ẘ":"w","ẉ":"w","ⱳ":"w","ⓧ":"x","ｘ":"x","ẋ":"x","ẍ":"x","ⓨ":"y","ｙ":"y","ỳ":"y","ý":"y","ŷ":"y","ỹ":"y","ȳ":"y","ẏ":"y","ÿ":"y","ỷ":"y","ẙ":"y","ỵ":"y","ƴ":"y","ɏ":"y","ỿ":"y","ⓩ":"z","ｚ":"z","ź":"z","ẑ":"z","ż":"z","ž":"z","ẓ":"z","ẕ":"z","ƶ":"z","ȥ":"z","ɀ":"z","ⱬ":"z","ꝣ":"z","Ά":"Α","Έ":"Ε","Ή":"Η","Ί":"Ι","Ϊ":"Ι","Ό":"Ο","Ύ":"Υ","Ϋ":"Υ","Ώ":"Ω","ά":"α","έ":"ε","ή":"η","ί":"ι","ϊ":"ι","ΐ":"ι","ό":"ο","ύ":"υ","ϋ":"υ","ΰ":"υ","ω":"ω","ς":"σ"};a=t(document),c=1,r=function(){return c++},i=N(n=N(Object,{bind:function(t){var e=this;return function(){t.apply(e,arguments)}},init:function(n){var i,s;this.opts=n=this.prepareOpts(n),this.id=n.id,n.element.data("select2")!==e&&null!==n.element.data("select2")&&n.element.data("select2").destroy(),this.container=this.createContainer(),this.liveRegion=t(".select2-hidden-accessible"),0==this.liveRegion.length&&(this.liveRegion=t("<span>",{role:"status","aria-live":"polite"}).addClass("select2-hidden-accessible").appendTo(document.body)),this.containerId="s2id_"+(n.element.attr("id")||"autogen"+r()),this.containerEventName=this.containerId.replace(/([.])/g,"_").replace(/([;&,\-\.\+\*\~':"\!\^#$%@\[\]\(\)=>\|])/g,"\\$1"),this.container.attr("id",this.containerId),this.container.attr("title",n.element.attr("title")),this.body=t(document.body),S(this.container,this.opts.element,this.opts.adaptContainerCssClass),this.container.attr("style",n.element.attr("style")),this.container.css(k(n.containerCss,this.opts.element)),this.container.addClass(k(n.containerCssClass,this.opts.element)),this.elementTabIndex=this.opts.element.attr("tabindex"),this.opts.element.data("select2",this).attr("tabindex","-1").before(this.container).on("click.select2",C),this.container.data("select2",this),this.dropdown=this.container.find(".select2-drop"),S(this.dropdown,this.opts.element,this.opts.adaptDropdownCssClass),this.dropdown.addClass(k(n.dropdownCssClass,this.opts.element)),this.dropdown.data("select2",this),this.dropdown.on("click",C),this.results=i=this.container.find(".select2-results"),this.search=s=this.container.find("input.select2-input"),this.queryCount=0,this.resultsPage=0,this.context=null,this.initContainer(),this.container.on("click",C),this.results.on("mousemove",function(n){var i=h;i!==e&&i.x===n.pageX&&i.y===n.pageY||t(n.target).trigger("mousemove-filtered",n)}),this.dropdown.on("mousemove-filtered",".select2-results",this.bind(this.highlightUnderEvent)),this.dropdown.on("touchstart touchmove touchend",".select2-results",this.bind(function(t){this._touchEvent=!0,this.highlightUnderEvent(t)})),this.dropdown.on("touchmove",".select2-results",this.bind(this.touchMoved)),this.dropdown.on("touchstart touchend",".select2-results",this.bind(this.clearTouchMoved)),this.dropdown.on("click",this.bind(function(t){this._touchEvent&&(this._touchEvent=!1,this.selectHighlighted())})),function(t,e){var n=w(t,function(t){e.trigger("scroll-debounced",t)});e.on("scroll",function(t){m(t.target,e.get())>=0&&n(t)})}(80,this.results),this.dropdown.on("scroll-debounced",".select2-results",this.bind(this.loadMoreIfNeeded)),t(this.container).on("change",".select2-input",function(t){t.stopPropagation()}),t(this.dropdown).on("change",".select2-input",function(t){t.stopPropagation()}),t.fn.mousewheel&&i.mousewheel(function(t,e,n,s){var r=i.scrollTop();s>0&&r-s<=0?(i.scrollTop(0),C(t)):s<0&&i.get(0).scrollHeight-i.scrollTop()+s<=i.height()&&(i.scrollTop(i.get(0).scrollHeight-i.height()),C(t))}),_(s),s.on("keyup-change input paste",this.bind(this.updateResults)),s.on("focus",function(){s.addClass("select2-focused")}),s.on("blur",function(){s.removeClass("select2-focused")}),this.dropdown.on("mouseup",".select2-results",this.bind(function(e){t(e.target).closest(".select2-result-selectable").length>0&&(this.highlightUnderEvent(e),this.selectHighlighted(e))})),this.dropdown.on("click mouseup mousedown touchstart touchend focusin",function(t){t.stopPropagation()}),this.nextSearchTerm=e,t.isFunction(this.opts.initSelection)&&(this.initSelection(),this.monitorSource()),null!==n.maximumInputLength&&this.search.attr("maxlength",n.maximumInputLength);var o=n.element.prop("disabled");o===e&&(o=!1),this.enable(!o);var a=n.element.prop("readonly");a===e&&(a=!1),this.readonly(a),l=l||function(){var e=t(d);e.appendTo(document.body);var n={width:e.width()-e[0].clientWidth,height:e.height()-e[0].clientHeight};return e.remove(),n}(),this.autofocus=n.element.prop("autofocus"),n.element.prop("autofocus",!1),this.autofocus&&this.focus(),this.search.attr("placeholder",n.searchInputPlaceholder)},destroy:function(){var t=this.opts.element,n=t.data("select2"),i=this;this.close(),t.length&&t[0].detachEvent&&i._sync&&t.each(function(){i._sync&&this.detachEvent("onpropertychange",i._sync)}),this.propertyObserver&&(this.propertyObserver.disconnect(),this.propertyObserver=null),this._sync=null,n!==e&&(n.container.remove(),n.liveRegion.remove(),n.dropdown.remove(),t.show().removeData("select2").off(".select2").prop("autofocus",this.autofocus||!1),this.elementTabIndex?t.attr({tabindex:this.elementTabIndex}):t.removeAttr("tabindex"),t.show()),O.call(this,"container","liveRegion","dropdown","results","search")},optionToData:function(t){return t.is("option")?{id:t.prop("value"),text:t.text(),element:t.get(),css:t.attr("class"),disabled:t.prop("disabled"),locked:v(t.attr("locked"),"locked")||v(t.data("locked"),!0)}:t.is("optgroup")?{text:t.attr("label"),children:[],element:t.get(),css:t.attr("class")}:void 0},prepareOpts:function(n){var i,s,o,a,l=this;if("select"===(i=n.element).get(0).tagName.toLowerCase()&&(this.select=s=n.element),s&&t.each(["id","multiple","ajax","query","createSearchChoice","initSelection","data","tags"],function(){if(this in n)throw new Error("Option '"+this+"' is not allowed for Select2 when attached to a <select> element.")}),"function"!=typeof(n=t.extend({},{populateResults:function(i,s,o){var a,c=this.opts.id,h=this.liveRegion;(a=function(i,s,u){var d,f,p,g,m,v,b,y,_,w,C=[];for(d=0,f=(i=n.sortResults(i,s,o)).length;d<f;d+=1)g=!(m=!0===(p=i[d]).disabled)&&c(p)!==e,v=p.children&&p.children.length>0,(b=t("<li></li>")).addClass("select2-results-dept-"+u),b.addClass("select2-result"),b.addClass(g?"select2-result-selectable":"select2-result-unselectable"),m&&b.addClass("select2-disabled"),v&&b.addClass("select2-result-with-children"),b.addClass(l.opts.formatResultCssClass(p)),b.attr("role","presentation"),(y=t(document.createElement("div"))).addClass("select2-result-label"),y.attr("id","select2-result-label-"+r()),y.attr("role","option"),(w=n.formatResult(p,y,o,l.opts.escapeMarkup))!==e&&(y.html(w),b.append(y)),v&&((_=t("<ul></ul>")).addClass("select2-result-sub"),a(p.children,_,u+1),b.append(_)),b.data("select2-data",p),C.push(b[0]);s.append(C),h.text(n.formatMatches(i.length))})(s,i,0)}},t.fn.select2.defaults,n)).id&&(o=n.id,n.id=function(t){return t[o]}),t.isArray(n.element.data("select2Tags"))){if("tags"in n)throw"tags specified as both an attribute 'data-select2-tags' and in options of Select2 "+n.element.attr("id");n.tags=n.element.data("select2Tags")}if(s?(n.query=this.bind(function(t){var n,s,r,o={results:[],more:!1},a=t.term;r=function(e,n){var i;e.is("option")?t.matcher(a,e.text(),e)&&n.push(l.optionToData(e)):e.is("optgroup")&&(i=l.optionToData(e),e.children().each2(function(t,e){r(e,i.children)}),i.children.length>0&&n.push(i))},n=i.children(),this.getPlaceholder()!==e&&n.length>0&&(s=this.getPlaceholderOption())&&(n=n.not(s)),n.each2(function(t,e){r(e,o.results)}),t.callback(o)}),n.id=function(t){return t.id}):"query"in n||("ajax"in n?((a=n.element.data("ajax-url"))&&a.length>0&&(n.ajax.url=a),n.query=D.call(n.element,n.ajax)):"data"in n?n.query=E(n.data):"tags"in n&&(n.query=I(n.tags),n.createSearchChoice===e&&(n.createSearchChoice=function(e){return{id:t.trim(e),text:t.trim(e)}}),n.initSelection===e&&(n.initSelection=function(e,i){var s=[];t(b(e.val(),n.separator,n.transformVal)).each(function(){var e={id:this,text:this},i=n.tags;t.isFunction(i)&&(i=i()),t(i).each(function(){if(v(this.id,e.id))return e=this,!1}),s.push(e)}),i(s)}))),"function"!=typeof n.query)throw"query function not defined for Select2 "+n.element.attr("id");if("top"===n.createSearchChoicePosition)n.createSearchChoicePosition=function(t,e){t.unshift(e)};else if("bottom"===n.createSearchChoicePosition)n.createSearchChoicePosition=function(t,e){t.push(e)};else if("function"!=typeof n.createSearchChoicePosition)throw"invalid createSearchChoicePosition option must be 'top', 'bottom' or a custom function";return n},monitorSource:function(){var n,i=this.opts.element,s=this;i.on("change.select2",this.bind(function(t){!0!==this.opts.element.data("select2-change-triggered")&&this.initSelection()})),this._sync=this.bind(function(){var t=i.prop("disabled");t===e&&(t=!1),this.enable(!t);var n=i.prop("readonly");n===e&&(n=!1),this.readonly(n),this.container&&(S(this.container,this.opts.element,this.opts.adaptContainerCssClass),this.container.addClass(k(this.opts.containerCssClass,this.opts.element))),this.dropdown&&(S(this.dropdown,this.opts.element,this.opts.adaptDropdownCssClass),this.dropdown.addClass(k(this.opts.dropdownCssClass,this.opts.element)))}),i.length&&i[0].attachEvent&&i.each(function(){this.attachEvent("onpropertychange",s._sync)}),(n=window.MutationObserver||window.WebKitMutationObserver||window.MozMutationObserver)!==e&&(this.propertyObserver&&(delete this.propertyObserver,this.propertyObserver=null),this.propertyObserver=new n(function(e){t.each(e,s._sync)}),this.propertyObserver.observe(i.get(0),{attributes:!0,subtree:!1}))},triggerSelect:function(e){var n=t.Event("select2-selecting",{val:this.id(e),object:e,choice:e});return this.opts.element.trigger(n),!n.isDefaultPrevented()},triggerChange:function(e){e=e||{},e=t.extend({},e,{type:"change",val:this.val()}),this.opts.element.data("select2-change-triggered",!0),this.opts.element.trigger(e),this.opts.element.data("select2-change-triggered",!1),this.opts.element.click(),this.opts.blurOnChange&&this.opts.element.blur()},isInterfaceEnabled:function(){return!0===this.enabledInterface},enableInterface:function(){var t=this._enabled&&!this._readonly,e=!t;return t!==this.enabledInterface&&(this.container.toggleClass("select2-container-disabled",e),this.close(),this.enabledInterface=t,!0)},enable:function(t){t===e&&(t=!0),this._enabled!==t&&(this._enabled=t,this.opts.element.prop("disabled",!t),this.enableInterface())},disable:function(){this.enable(!1)},readonly:function(t){t===e&&(t=!1),this._readonly!==t&&(this._readonly=t,this.opts.element.prop("readonly",t),this.enableInterface())},opened:function(){return!!this.container&&this.container.hasClass("select2-dropdown-open")},positionDropdown:function(){var e,n,i,s,r,o=this.dropdown,a=this.container,c=a.offset(),h=a.outerHeight(!1),u=a.outerWidth(!1),d=o.outerHeight(!1),f=t(window),p=f.width(),g=f.height(),m=f.scrollLeft()+p,v=f.scrollTop()+g,b=c.top+h,y=c.left,_=b+d<=v,w=c.top-d>=f.scrollTop(),C=o.outerWidth(!1);o.hasClass("select2-drop-above")?(n=!0,!w&&_&&(i=!0,n=!1)):(n=!1,!_&&w&&(i=!0,n=!0)),i&&(o.hide(),c=this.container.offset(),h=this.container.outerHeight(!1),u=this.container.outerWidth(!1),d=o.outerHeight(!1),m=f.scrollLeft()+p,v=f.scrollTop()+g,b=c.top+h,y=c.left,C=o.outerWidth(!1),o.show(),this.focusSearch()),this.opts.dropdownAutoWidth?(r=t(".select2-results",o)[0],o.addClass("select2-drop-auto-width"),o.css("width",""),(C=o.outerWidth(!1)+(r.scrollHeight===r.clientHeight?0:l.width))>u?u=C:C=u,d=o.outerHeight(!1)):this.container.removeClass("select2-drop-auto-width"),"static"!==this.body.css("position")&&(b-=(e=this.body.offset()).top,y-=e.left),!(y+C<=m)&&c.left+m+a.outerWidth(!1)>C&&(y=c.left+this.container.outerWidth(!1)-C),s={left:y,width:u},n?(s.top=c.top-d,s.bottom="auto",this.container.addClass("select2-drop-above"),o.addClass("select2-drop-above")):(s.top=b,s.bottom="auto",this.container.removeClass("select2-drop-above"),o.removeClass("select2-drop-above")),s=t.extend(s,k(this.opts.dropdownCss,this.opts.element)),o.css(s)},shouldOpen:function(){var e;return!this.opened()&&(!1!==this._enabled&&!0!==this._readonly&&(e=t.Event("select2-opening"),this.opts.element.trigger(e),!e.isDefaultPrevented()))},clearDropdownAlignmentPreference:function(){this.container.removeClass("select2-drop-above"),this.dropdown.removeClass("select2-drop-above")},open:function(){return!!this.shouldOpen()&&(this.opening(),a.on("mousemove.select2Event",function(t){h.x=t.pageX,h.y=t.pageY}),!0)},opening:function(){var e,n=this.containerEventName,i="scroll."+n,s="resize."+n,r="orientationchange."+n;this.container.addClass("select2-dropdown-open").addClass("select2-container-active"),this.clearDropdownAlignmentPreference(),this.dropdown[0]!==this.body.children().last()[0]&&this.dropdown.detach().appendTo(this.body),0===(e=t("#select2-drop-mask")).length&&((e=t(document.createElement("div"))).attr("id","select2-drop-mask").attr("class","select2-drop-mask"),e.hide(),e.appendTo(this.body),e.on("mousedown touchstart click",function(n){p(e);var i,s=t("#select2-drop");s.length>0&&((i=s.data("select2")).opts.selectOnBlur&&i.selectHighlighted({noFocus:!0}),i.close(),n.preventDefault(),n.stopPropagation())})),this.dropdown.prev()[0]!==e[0]&&this.dropdown.before(e),t("#select2-drop").removeAttr("id"),this.dropdown.attr("id","select2-drop"),e.show(),this.positionDropdown(),this.dropdown.show(),this.positionDropdown(),this.dropdown.addClass("select2-drop-active");var o=this;this.container.parents().add(window).each(function(){t(this).on(s+" "+i+" "+r,function(t){o.opened()&&o.positionDropdown()})})},close:function(){if(this.opened()){var e=this.containerEventName,n="scroll."+e,i="resize."+e,s="orientationchange."+e;this.container.parents().add(window).each(function(){t(this).off(n).off(i).off(s)}),this.clearDropdownAlignmentPreference(),t("#select2-drop-mask").hide(),this.dropdown.removeAttr("id"),this.dropdown.hide(),this.container.removeClass("select2-dropdown-open").removeClass("select2-container-active"),this.results.empty(),a.off("mousemove.select2Event"),this.clearSearch(),this.search.removeClass("select2-active"),this.opts.element.trigger(t.Event("select2-close"))}},externalSearch:function(t){this.open(),this.search.val(t),this.updateResults(!1)},clearSearch:function(){},getMaximumSelectionSize:function(){return k(this.opts.maximumSelectionSize,this.opts.element)},ensureHighlightVisible:function(){var e,n,i,s,r,o,a,l,c=this.results;(n=this.highlight())<0||(0!=n?(e=this.findHighlightableChoices().find(".select2-result-label"),s=(l=((i=t(e[n])).offset()||{}).top||0)+i.outerHeight(!0),n===e.length-1&&(a=c.find("li.select2-more-results")).length>0&&(s=a.offset().top+a.outerHeight(!0)),s>(r=c.offset().top+c.outerHeight(!1))&&c.scrollTop(c.scrollTop()+(s-r)),(o=l-c.offset().top)<0&&"none"!=i.css("display")&&c.scrollTop(c.scrollTop()+o)):c.scrollTop(0))},findHighlightableChoices:function(){return this.results.find(".select2-result-selectable:not(.select2-disabled):not(.select2-selected)")},moveHighlight:function(e){for(var n=this.findHighlightableChoices(),i=this.highlight();i>-1&&i<n.length;){var s=t(n[i+=e]);if(s.hasClass("select2-result-selectable")&&!s.hasClass("select2-disabled")&&!s.hasClass("select2-selected")){this.highlight(i);break}}},highlight:function(e){var n,i,s=this.findHighlightableChoices();if(0===arguments.length)return m(s.filter(".select2-highlighted")[0],s.get());e>=s.length&&(e=s.length-1),e<0&&(e=0),this.removeHighlight(),(n=t(s[e])).addClass("select2-highlighted"),this.search.attr("aria-activedescendant",n.find(".select2-result-label").attr("id")),this.ensureHighlightVisible(),this.liveRegion.text(n.text()),(i=n.data("select2-data"))&&this.opts.element.trigger({type:"select2-highlight",val:this.id(i),choice:i})},removeHighlight:function(){this.results.find(".select2-highlighted").removeClass("select2-highlighted")},touchMoved:function(){this._touchMoved=!0},clearTouchMoved:function(){this._touchMoved=!1},countSelectableResults:function(){return this.findHighlightableChoices().length},highlightUnderEvent:function(e){var n=t(e.target).closest(".select2-result-selectable");if(n.length>0&&!n.is(".select2-highlighted")){var i=this.findHighlightableChoices();this.highlight(i.index(n))}else 0==n.length&&this.removeHighlight()},loadMoreIfNeeded:function(){var t=this.results,e=t.find("li.select2-more-results"),n=this.resultsPage+1,i=this,s=this.search.val(),r=this.context;0!==e.length&&e.offset().top-t.offset().top-t.height()<=this.opts.loadMorePadding&&(e.addClass("select2-active"),this.opts.query({element:this.opts.element,term:s,page:n,context:r,matcher:this.opts.matcher,callback:this.bind(function(o){i.opened()&&(i.opts.populateResults.call(this,t,o.results,{term:s,page:n,context:r}),i.postprocessResults(o,!1,!1),!0===o.more?(e.detach().appendTo(t).html(i.opts.escapeMarkup(k(i.opts.formatLoadMore,i.opts.element,n+1))),window.setTimeout(function(){i.loadMoreIfNeeded()},10)):e.remove(),i.positionDropdown(),i.resultsPage=n,i.context=o.context,this.opts.element.trigger({type:"select2-loaded",items:o}))})}))},tokenize:function(){},updateResults:function(n){var i,s,r,o=this.search,a=this.results,l=this.opts,c=this,h=o.val(),u=t.data(this.container,"select2-last-term");if((!0===n||!u||!v(h,u))&&(t.data(this.container,"select2-last-term",h),!0===n||!1!==this.showSearchInput&&this.opened())){r=++this.queryCount;var d=this.getMaximumSelectionSize();if(!(d>=1&&(i=this.data(),t.isArray(i)&&i.length>=d&&A(l.formatSelectionTooBig,"formatSelectionTooBig"))))return o.val().length<l.minimumInputLength?(A(l.formatInputTooShort,"formatInputTooShort")?p("<li class='select2-no-results'>"+k(l.formatInputTooShort,l.element,o.val(),l.minimumInputLength)+"</li>"):p(""),void(n&&this.showSearch&&this.showSearch(!0))):void(l.maximumInputLength&&o.val().length>l.maximumInputLength?A(l.formatInputTooLong,"formatInputTooLong")?p("<li class='select2-no-results'>"+k(l.formatInputTooLong,l.element,o.val(),l.maximumInputLength)+"</li>"):p(""):(l.formatSearching&&0===this.findHighlightableChoices().length&&p("<li class='select2-searching'>"+k(l.formatSearching,l.element)+"</li>"),o.addClass("select2-active"),this.removeHighlight(),(s=this.tokenize())!=e&&null!=s&&o.val(s),this.resultsPage=1,l.query({element:l.element,term:o.val(),page:this.resultsPage,context:null,matcher:l.matcher,callback:this.bind(function(i){var s;r==this.queryCount&&(this.opened()?i.hasError!==e&&A(l.formatAjaxError,"formatAjaxError")?p("<li class='select2-ajax-error'>"+k(l.formatAjaxError,l.element,i.jqXHR,i.textStatus,i.errorThrown)+"</li>"):(this.context=i.context===e?null:i.context,this.opts.createSearchChoice&&""!==o.val()&&(s=this.opts.createSearchChoice.call(c,o.val(),i.results))!==e&&null!==s&&c.id(s)!==e&&null!==c.id(s)&&0===t(i.results).filter(function(){return v(c.id(this),c.id(s))}).length&&this.opts.createSearchChoicePosition(i.results,s),0===i.results.length&&A(l.formatNoMatches,"formatNoMatches")?p("<li class='select2-no-results'>"+k(l.formatNoMatches,l.element,o.val())+"</li>"):(a.empty(),c.opts.populateResults.call(this,a,i.results,{term:o.val(),page:this.resultsPage,context:null}),!0===i.more&&A(l.formatLoadMore,"formatLoadMore")&&(a.append("<li class='select2-more-results'>"+l.escapeMarkup(k(l.formatLoadMore,l.element,this.resultsPage))+"</li>"),window.setTimeout(function(){c.loadMoreIfNeeded()},10)),this.postprocessResults(i,n),f(),this.opts.element.trigger({type:"select2-loaded",items:i}))):this.search.removeClass("select2-active"))})})));p("<li class='select2-selection-limit'>"+k(l.formatSelectionTooBig,l.element,d)+"</li>")}function f(){o.removeClass("select2-active"),c.positionDropdown(),a.find(".select2-no-results,.select2-selection-limit,.select2-searching").length?c.liveRegion.text(a.text()):c.liveRegion.text(c.opts.formatMatches(a.find('.select2-result-selectable:not(".select2-selected")').length))}function p(t){a.html(t),f()}},cancel:function(){this.close()},blur:function(){this.opts.selectOnBlur&&this.selectHighlighted({noFocus:!0}),this.close(),this.container.removeClass("select2-container-active"),this.search[0]===document.activeElement&&this.search.blur(),this.clearSearch(),this.selection.find(".select2-search-choice-focus").removeClass("select2-search-choice-focus")},focusSearch:function(){var t;(t=this.search)[0]!==document.activeElement&&window.setTimeout(function(){var e,n=t[0],i=t.val().length;t.focus(),(n.offsetWidth>0||n.offsetHeight>0)&&n===document.activeElement&&(n.setSelectionRange?n.setSelectionRange(i,i):n.createTextRange&&((e=n.createTextRange()).collapse(!1),e.select()))},0)},selectHighlighted:function(t){if(this._touchMoved)this.clearTouchMoved();else{var e=this.highlight(),n=this.results.find(".select2-highlighted").closest(".select2-result").data("select2-data");n?(this.highlight(e),this.onSelect(n,t)):t&&t.noFocus&&this.close()}},getPlaceholder:function(){var t;return this.opts.element.attr("placeholder")||this.opts.element.attr("data-placeholder")||this.opts.element.data("placeholder")||this.opts.placeholder||((t=this.getPlaceholderOption())!==e?t.text():e)},getPlaceholderOption:function(){if(this.select){var n=this.select.children("option").first();if(this.opts.placeholderOption!==e)return"first"===this.opts.placeholderOption&&n||"function"==typeof this.opts.placeholderOption&&this.opts.placeholderOption(this.select);if(""===t.trim(n.text())&&""===n.val())return n}},initContainerWidth:function(){var n=function(){var n,i,s,r,o;if("off"===this.opts.width)return null;if("element"===this.opts.width)return 0===this.opts.element.outerWidth(!1)?"auto":this.opts.element.outerWidth(!1)+"px";if("copy"===this.opts.width||"resolve"===this.opts.width){if((n=this.opts.element.attr("style"))!==e)for(r=0,o=(i=n.split(";")).length;r<o;r+=1)if(null!==(s=i[r].replace(/\s/g,"").match(/^width:(([-+]?([0-9]*\.)?[0-9]+)(px|em|ex|%|in|cm|mm|pt|pc))/i))&&s.length>=1)return s[1];return"resolve"===this.opts.width?(n=this.opts.element.css("width")).indexOf("%")>0?n:0===this.opts.element.outerWidth(!1)?"auto":this.opts.element.outerWidth(!1)+"px":null}return t.isFunction(this.opts.width)?this.opts.width():this.opts.width}.call(this);null!==n&&this.container.css("width",n)}}),{createContainer:function(){return t(document.createElement("div")).attr({class:"select2-container"}).html(["<a href='javascript:void(0)' class='select2-choice' tabindex='-1'>","   <span class='select2-chosen'>&#160;</span><abbr class='select2-search-choice-close'></abbr>","   <span class='select2-arrow' role='presentation'><b role='presentation'></b></span>","</a>","<label for='' class='select2-offscreen'></label>","<input class='select2-focusser select2-offscreen' type='text' aria-haspopup='true' role='button' />","<div class='select2-drop select2-display-none'>","   <div class='select2-search'>","       <label for='' class='select2-offscreen'></label>","       <input type='text' autocomplete='off' autocorrect='off' autocapitalize='off' spellcheck='false' class='select2-input' role='combobox' aria-expanded='true'","       aria-autocomplete='list' />","   </div>","   <ul class='select2-results' role='listbox'>","   </ul>","</div>"].join(""))},enableInterface:function(){this.parent.enableInterface.apply(this,arguments)&&this.focusser.prop("disabled",!this.isInterfaceEnabled())},opening:function(){var n,i,s;this.opts.minimumResultsForSearch>=0&&this.showSearch(!0),this.parent.opening.apply(this,arguments),!1!==this.showSearchInput&&this.search.val(this.focusser.val()),this.opts.shouldFocusInput(this)&&(this.search.focus(),(n=this.search.get(0)).createTextRange?((i=n.createTextRange()).collapse(!1),i.select()):n.setSelectionRange&&(s=this.search.val().length,n.setSelectionRange(s,s))),""===this.search.val()&&this.nextSearchTerm!=e&&(this.search.val(this.nextSearchTerm),this.search.select()),this.focusser.prop("disabled",!0).val(""),this.updateResults(!0),this.opts.element.trigger(t.Event("select2-open"))},close:function(){this.opened()&&(this.parent.close.apply(this,arguments),this.focusser.prop("disabled",!1),this.opts.shouldFocusInput(this)&&this.focusser.focus())},focus:function(){this.opened()?this.close():(this.focusser.prop("disabled",!1),this.opts.shouldFocusInput(this)&&this.focusser.focus())},isFocused:function(){return this.container.hasClass("select2-container-active")},cancel:function(){this.parent.cancel.apply(this,arguments),this.focusser.prop("disabled",!1),this.opts.shouldFocusInput(this)&&this.focusser.focus()},destroy:function(){t("label[for='"+this.focusser.attr("id")+"']").attr("for",this.opts.element.attr("id")),this.parent.destroy.apply(this,arguments),O.call(this,"selection","focusser")},initContainer:function(){var e,n,i=this.container,s=this.dropdown,o=r();this.opts.minimumResultsForSearch<0?this.showSearch(!1):this.showSearch(!0),this.selection=e=i.find(".select2-choice"),this.focusser=i.find(".select2-focusser"),e.find(".select2-chosen").attr("id","select2-chosen-"+o),this.focusser.attr("aria-labelledby","select2-chosen-"+o),this.results.attr("id","select2-results-"+o),this.search.attr("aria-owns","select2-results-"+o),this.focusser.attr("id","s2id_autogen"+o),n=t("label[for='"+this.opts.element.attr("id")+"']"),this.opts.element.focus(this.bind(function(){this.focus()})),this.focusser.prev().text(n.text()).attr("for",this.focusser.attr("id"));var a=this.opts.element.attr("title");this.opts.element.attr("title",a||n.text()),this.focusser.attr("tabindex",this.elementTabIndex),this.search.attr("id",this.focusser.attr("id")+"_search"),this.search.prev().text(t("label[for='"+this.focusser.attr("id")+"']").text()).attr("for",this.search.attr("id")),this.search.on("keydown",this.bind(function(t){if(this.isInterfaceEnabled()&&229!=t.keyCode)if(t.which!==u.PAGE_UP&&t.which!==u.PAGE_DOWN)switch(t.which){case u.UP:case u.DOWN:return this.moveHighlight(t.which===u.UP?-1:1),void C(t);case u.ENTER:return this.selectHighlighted(),void C(t);case u.TAB:return void this.selectHighlighted({noFocus:!0});case u.ESC:return this.cancel(t),void C(t)}else C(t)})),this.search.on("blur",this.bind(function(t){document.activeElement===this.body.get(0)&&window.setTimeout(this.bind(function(){this.opened()&&this.search.focus()}),0)})),this.focusser.on("keydown",this.bind(function(t){if(this.isInterfaceEnabled()&&t.which!==u.TAB&&!u.isControl(t)&&!u.isFunctionKey(t)&&t.which!==u.ESC){if(!1!==this.opts.openOnEnter||t.which!==u.ENTER){if(t.which==u.DOWN||t.which==u.UP||t.which==u.ENTER&&this.opts.openOnEnter){if(t.altKey||t.ctrlKey||t.shiftKey||t.metaKey)return;return this.open(),void C(t)}return t.which==u.DELETE||t.which==u.BACKSPACE?(this.opts.allowClear&&this.clear(),void C(t)):void 0}C(t)}})),_(this.focusser),this.focusser.on("keyup-change input",this.bind(function(t){if(this.opts.minimumResultsForSearch>=0){if(t.stopPropagation(),this.opened())return;this.open()}})),e.on("mousedown touchstart","abbr",this.bind(function(t){var e;this.isInterfaceEnabled()&&(this.clear(),(e=t).preventDefault(),e.stopImmediatePropagation(),this.close(),this.selection&&this.selection.focus())})),e.on("mousedown touchstart",this.bind(function(n){p(e),this.container.hasClass("select2-container-active")||this.opts.element.trigger(t.Event("select2-focus")),this.opened()?this.close():this.isInterfaceEnabled()&&this.open(),C(n)})),s.on("mousedown touchstart",this.bind(function(){this.opts.shouldFocusInput(this)&&this.search.focus()})),e.on("focus",this.bind(function(t){C(t)})),this.focusser.on("focus",this.bind(function(){this.container.hasClass("select2-container-active")||this.opts.element.trigger(t.Event("select2-focus")),this.container.addClass("select2-container-active")})).on("blur",this.bind(function(){this.opened()||(this.container.removeClass("select2-container-active"),this.opts.element.trigger(t.Event("select2-blur")))})),this.search.on("focus",this.bind(function(){this.container.hasClass("select2-container-active")||this.opts.element.trigger(t.Event("select2-focus")),this.container.addClass("select2-container-active")})),this.initContainerWidth(),this.opts.element.hide(),this.setPlaceholder()},clear:function(e){var n=this.selection.data("select2-data");if(n){var i=t.Event("select2-clearing");if(this.opts.element.trigger(i),i.isDefaultPrevented())return;var s=this.getPlaceholderOption();this.opts.element.val(s?s.val():""),this.selection.find(".select2-chosen").empty(),this.selection.removeData("select2-data"),this.setPlaceholder(),!1!==e&&(this.opts.element.trigger({type:"select2-removed",val:this.id(n),choice:n}),this.triggerChange({removed:n}))}},initSelection:function(){if(this.isPlaceholderOptionSelected())this.updateSelection(null),this.close(),this.setPlaceholder();else{var t=this;this.opts.initSelection.call(null,this.opts.element,function(n){n!==e&&null!==n&&(t.updateSelection(n),t.close(),t.setPlaceholder(),t.nextSearchTerm=t.opts.nextSearchTerm(n,t.search.val()))})}},isPlaceholderOptionSelected:function(){var t;return this.getPlaceholder()!==e&&((t=this.getPlaceholderOption())!==e&&t.prop("selected")||""===this.opts.element.val()||this.opts.element.val()===e||null===this.opts.element.val())},prepareOpts:function(){var e=this.parent.prepareOpts.apply(this,arguments),n=this;return"select"===e.element.get(0).tagName.toLowerCase()?e.initSelection=function(t,e){var i=t.find("option").filter(function(){return this.selected&&!this.disabled});e(n.optionToData(i))}:"data"in e&&(e.initSelection=e.initSelection||function(n,i){var s=n.val(),r=null;e.query({matcher:function(t,n,i){var o=v(s,e.id(i));return o&&(r=i),o},callback:t.isFunction(i)?function(){i(r)}:t.noop})}),e},getPlaceholder:function(){return this.select&&this.getPlaceholderOption()===e?e:this.parent.getPlaceholder.apply(this,arguments)},setPlaceholder:function(){var t=this.getPlaceholder();if(this.isPlaceholderOptionSelected()&&t!==e){if(this.select&&this.getPlaceholderOption()===e)return;this.selection.find(".select2-chosen").html(this.opts.escapeMarkup(t)),this.selection.addClass("select2-default"),this.container.removeClass("select2-allowclear")}},postprocessResults:function(e,n,i){var s=0,r=this;if(this.findHighlightableChoices().each2(function(t,e){if(v(r.id(e.data("select2-data")),r.opts.element.val()))return s=t,!1}),!1!==i&&(!0===n&&s>=0?this.highlight(s):this.highlight(0)),!0===n){var o=this.opts.minimumResultsForSearch;o>=0&&this.showSearch(function e(n){var i=0;return t.each(n,function(t,n){n.children?i+=e(n.children):i++}),i}(e.results)>=o)}},showSearch:function(e){this.showSearchInput!==e&&(this.showSearchInput=e,this.dropdown.find(".select2-search").toggleClass("select2-search-hidden",!e),this.dropdown.find(".select2-search").toggleClass("select2-offscreen",!e),t(this.dropdown,this.container).toggleClass("select2-with-searchbox",e))},onSelect:function(t,e){if(this.triggerSelect(t)){var n=this.opts.element.val(),i=this.data();this.opts.element.val(this.id(t)),this.updateSelection(t),this.opts.element.trigger({type:"select2-selected",val:this.id(t),choice:t}),this.nextSearchTerm=this.opts.nextSearchTerm(t,this.search.val()),this.close(),e&&e.noFocus||!this.opts.shouldFocusInput(this)||this.focusser.focus(),v(n,this.id(t))||this.triggerChange({added:t,removed:i})}},updateSelection:function(t){var n,i,s=this.selection.find(".select2-chosen");this.selection.data("select2-data",t),s.empty(),null!==t&&(n=this.opts.formatSelection(t,s,this.opts.escapeMarkup)),n!==e&&s.append(n),(i=this.opts.formatSelectionCssClass(t,s))!==e&&s.addClass(i),this.selection.removeClass("select2-default"),this.opts.allowClear&&this.getPlaceholder()!==e&&this.container.addClass("select2-allowclear")},val:function(){var t,n=!1,i=null,s=this,r=this.data();if(0===arguments.length)return this.opts.element.val();if(t=arguments[0],arguments.length>1&&(n=arguments[1]),this.select)this.select.val(t).find("option").filter(function(){return this.selected}).each2(function(t,e){return i=s.optionToData(e),!1}),this.updateSelection(i),this.setPlaceholder(),n&&this.triggerChange({added:i,removed:r});else{if(!t&&0!==t)return void this.clear(n);if(this.opts.initSelection===e)throw new Error("cannot call val() if initSelection() is not defined");this.opts.element.val(t),this.opts.initSelection(this.opts.element,function(t){s.opts.element.val(t?s.id(t):""),s.updateSelection(t),s.setPlaceholder(),n&&s.triggerChange({added:t,removed:r})})}},clearSearch:function(){this.search.val(""),this.focusser.val("")},data:function(t){var n,i=!1;if(0===arguments.length)return(n=this.selection.data("select2-data"))==e&&(n=null),n;arguments.length>1&&(i=arguments[1]),t?(n=this.data(),this.opts.element.val(t?this.id(t):""),this.updateSelection(t),i&&this.triggerChange({added:t,removed:n})):this.clear(i)}}),s=N(n,{createContainer:function(){return t(document.createElement("div")).attr({class:"select2-container select2-container-multi"}).html(["<ul class='select2-choices'>","  <li class='select2-search-field'>","    <label for='' class='select2-offscreen'></label>","    <input type='text' autocomplete='off' autocorrect='off' autocapitalize='off' spellcheck='false' class='select2-input'>","  </li>","</ul>","<div class='select2-drop select2-drop-multi select2-display-none'>","   <ul class='select2-results'>","   </ul>","</div>"].join(""))},prepareOpts:function(){var e=this.parent.prepareOpts.apply(this,arguments),n=this;return"select"===e.element.get(0).tagName.toLowerCase()?e.initSelection=function(t,e){var i=[];t.find("option").filter(function(){return this.selected&&!this.disabled}).each2(function(t,e){i.push(n.optionToData(e))}),e(i)}:"data"in e&&(e.initSelection=e.initSelection||function(n,i){var s=b(n.val(),e.separator,e.transformVal),r=[];e.query({matcher:function(n,i,o){var a=t.grep(s,function(t){return v(t,e.id(o))}).length;return a&&r.push(o),a},callback:t.isFunction(i)?function(){for(var t=[],n=0;n<s.length;n++)for(var o=s[n],a=0;a<r.length;a++){var l=r[a];if(v(o,e.id(l))){t.push(l),r.splice(a,1);break}}i(t)}:t.noop})}),e},selectChoice:function(t){var e=this.container.find(".select2-search-choice-focus");e.length&&t&&t[0]==e[0]||(e.length&&this.opts.element.trigger("choice-deselected",e),e.removeClass("select2-search-choice-focus"),t&&t.length&&(this.close(),t.addClass("select2-search-choice-focus"),this.opts.element.trigger("choice-selected",t)))},destroy:function(){t("label[for='"+this.search.attr("id")+"']").attr("for",this.opts.element.attr("id")),this.parent.destroy.apply(this,arguments),O.call(this,"searchContainer","selection")},initContainer:function(){var e,n=".select2-choices";this.searchContainer=this.container.find(".select2-search-field"),this.selection=e=this.container.find(n);var i=this;this.selection.on("click",".select2-container:not(.select2-container-disabled) .select2-search-choice:not(.select2-locked)",function(e){i.search[0].focus(),i.selectChoice(t(this))}),this.search.attr("id","s2id_autogen"+r()),this.search.prev().text(t("label[for='"+this.opts.element.attr("id")+"']").text()).attr("for",this.search.attr("id")),this.opts.element.focus(this.bind(function(){this.focus()})),this.search.on("input paste",this.bind(function(){this.search.attr("placeholder")&&0==this.search.val().length||this.isInterfaceEnabled()&&(this.opened()||this.open())})),this.search.attr("tabindex",this.elementTabIndex),this.keydowns=0,this.search.on("keydown",this.bind(function(n){if(this.isInterfaceEnabled()){++this.keydowns;var i=e.find(".select2-search-choice-focus"),s=i.prev(".select2-search-choice:not(.select2-locked)"),r=i.next(".select2-search-choice:not(.select2-locked)"),o=function(e){var n=0,i=0;if("selectionStart"in(e=t(e)[0]))n=e.selectionStart,i=e.selectionEnd-n;else if("selection"in document){e.focus();var s=document.selection.createRange();i=document.selection.createRange().text.length,s.moveStart("character",-e.value.length),n=s.text.length-i}return{offset:n,length:i}}(this.search);if(i.length&&(n.which==u.LEFT||n.which==u.RIGHT||n.which==u.BACKSPACE||n.which==u.DELETE||n.which==u.ENTER)){var a=i;return n.which==u.LEFT&&s.length?a=s:n.which==u.RIGHT?a=r.length?r:null:n.which===u.BACKSPACE?this.unselect(i.first())&&(this.search.width(10),a=s.length?s:r):n.which==u.DELETE?this.unselect(i.first())&&(this.search.width(10),a=r.length?r:null):n.which==u.ENTER&&(a=null),this.selectChoice(a),C(n),void(a&&a.length||this.open())}if((n.which===u.BACKSPACE&&1==this.keydowns||n.which==u.LEFT)&&0==o.offset&&!o.length)return this.selectChoice(e.find(".select2-search-choice:not(.select2-locked)").last()),void C(n);if(this.selectChoice(null),this.opened())switch(n.which){case u.UP:case u.DOWN:return this.moveHighlight(n.which===u.UP?-1:1),void C(n);case u.ENTER:return this.selectHighlighted(),void C(n);case u.TAB:return this.selectHighlighted({noFocus:!0}),void this.close();case u.ESC:return this.cancel(n),void C(n)}if(n.which!==u.TAB&&!u.isControl(n)&&!u.isFunctionKey(n)&&n.which!==u.BACKSPACE&&n.which!==u.ESC){if(n.which===u.ENTER){if(!1===this.opts.openOnEnter)return;if(n.altKey||n.ctrlKey||n.shiftKey||n.metaKey)return}this.open(),n.which!==u.PAGE_UP&&n.which!==u.PAGE_DOWN||C(n),n.which===u.ENTER&&C(n)}}})),this.search.on("keyup",this.bind(function(t){this.keydowns=0,this.resizeSearch()})),this.search.on("blur",this.bind(function(e){this.container.removeClass("select2-container-active"),this.search.removeClass("select2-focused"),this.selectChoice(null),this.opened()||this.clearSearch(),e.stopImmediatePropagation(),this.opts.element.trigger(t.Event("select2-blur"))})),this.container.on("click",n,this.bind(function(e){this.isInterfaceEnabled()&&(t(e.target).closest(".select2-search-choice").length>0||(this.selectChoice(null),this.clearPlaceholder(),this.container.hasClass("select2-container-active")||this.opts.element.trigger(t.Event("select2-focus")),this.open(),this.focusSearch(),e.preventDefault()))})),this.container.on("focus",n,this.bind(function(){this.isInterfaceEnabled()&&(this.container.hasClass("select2-container-active")||this.opts.element.trigger(t.Event("select2-focus")),this.container.addClass("select2-container-active"),this.dropdown.addClass("select2-drop-active"),this.clearPlaceholder())})),this.initContainerWidth(),this.opts.element.hide(),this.clearSearch()},enableInterface:function(){this.parent.enableInterface.apply(this,arguments)&&this.search.prop("disabled",!this.isInterfaceEnabled())},initSelection:function(){if(""===this.opts.element.val()&&""===this.opts.element.text()&&(this.updateSelection([]),this.close(),this.clearSearch()),this.select||""!==this.opts.element.val()){var t=this;this.opts.initSelection.call(null,this.opts.element,function(n){n!==e&&null!==n&&(t.updateSelection(n),t.close(),t.clearSearch())})}},clearSearch:function(){var t=this.getPlaceholder(),n=this.getMaxSearchWidth();t!==e&&0===this.getVal().length&&!1===this.search.hasClass("select2-focused")?(this.search.val(t).addClass("select2-default"),this.search.width(n>0?n:this.container.css("width"))):this.search.val("").width(10)},clearPlaceholder:function(){this.search.hasClass("select2-default")&&this.search.val("").removeClass("select2-default")},opening:function(){this.clearPlaceholder(),this.resizeSearch(),this.parent.opening.apply(this,arguments),this.focusSearch(),""===this.search.val()&&this.nextSearchTerm!=e&&(this.search.val(this.nextSearchTerm),this.search.select()),this.updateResults(!0),this.opts.shouldFocusInput(this)&&this.search.focus(),this.opts.element.trigger(t.Event("select2-open"))},close:function(){this.opened()&&this.parent.close.apply(this,arguments)},focus:function(){this.close(),this.search.focus()},isFocused:function(){return this.search.hasClass("select2-focused")},updateSelection:function(e){var n=[],i=[],s=this;t(e).each(function(){m(s.id(this),n)<0&&(n.push(s.id(this)),i.push(this))}),e=i,this.selection.find(".select2-search-choice").remove(),t(e).each(function(){s.addSelectedChoice(this)}),s.postprocessResults()},tokenize:function(){var t=this.search.val();null!=(t=this.opts.tokenizer.call(this,t,this.data(),this.bind(this.onSelect),this.opts))&&t!=e&&(this.search.val(t),t.length>0&&this.open())},onSelect:function(t,n){this.triggerSelect(t)&&""!==t.text&&(this.addSelectedChoice(t),this.opts.element.trigger({type:"selected",val:this.id(t),choice:t}),this.nextSearchTerm=this.opts.nextSearchTerm(t,this.search.val()),this.clearSearch(),this.updateResults(),!this.select&&this.opts.closeOnSelect||this.postprocessResults(t,!1,!0===this.opts.closeOnSelect),this.opts.closeOnSelect?(this.close(),this.search.width(10)):this.countSelectableResults()>0?(this.search.width(10),this.resizeSearch(),this.getMaximumSelectionSize()>0&&this.val().length>=this.getMaximumSelectionSize()?this.updateResults(!0):this.nextSearchTerm!=e&&(this.search.val(this.nextSearchTerm),this.updateResults(),this.search.select()),this.positionDropdown()):(this.close(),this.search.width(10)),this.triggerChange({added:t}),n&&n.noFocus||this.focusSearch())},cancel:function(){this.close(),this.focusSearch()},addSelectedChoice:function(n){var i,s,r=!n.locked,o=t("<li class='select2-search-choice'>    <div></div>    <a href='#' class='select2-search-choice-close' tabindex='-1'></a></li>"),a=t("<li class='select2-search-choice select2-locked'><div></div></li>"),l=r?o:a,c=this.id(n),h=this.getVal();(i=this.opts.formatSelection(n,l.find("div"),this.opts.escapeMarkup))!=e&&l.find("div").replaceWith(t("<div></div>").html(i)),(s=this.opts.formatSelectionCssClass(n,l.find("div")))!=e&&l.addClass(s),r&&l.find(".select2-search-choice-close").on("mousedown",C).on("click dblclick",this.bind(function(e){this.isInterfaceEnabled()&&(this.unselect(t(e.target)),this.selection.find(".select2-search-choice-focus").removeClass("select2-search-choice-focus"),C(e),this.close(),this.focusSearch())})).on("focus",this.bind(function(){this.isInterfaceEnabled()&&(this.container.addClass("select2-container-active"),this.dropdown.addClass("select2-drop-active"))})),l.data("select2-data",n),l.insertBefore(this.searchContainer),h.push(c),this.setVal(h)},unselect:function(e){var n,i,s=this.getVal();if(0===(e=e.closest(".select2-search-choice")).length)throw"Invalid argument: "+e+". Must be .select2-search-choice";if(n=e.data("select2-data")){var r=t.Event("select2-removing");if(r.val=this.id(n),r.choice=n,this.opts.element.trigger(r),r.isDefaultPrevented())return!1;for(;(i=m(this.id(n),s))>=0;)s.splice(i,1),this.setVal(s),this.select&&this.postprocessResults();return e.remove(),this.opts.element.trigger({type:"select2-removed",val:this.id(n),choice:n}),this.triggerChange({removed:n}),!0}},postprocessResults:function(t,e,n){var i=this.getVal(),s=this.results.find(".select2-result"),r=this.results.find(".select2-result-with-children"),o=this;s.each2(function(t,e){m(o.id(e.data("select2-data")),i)>=0&&(e.addClass("select2-selected"),e.find(".select2-result-selectable").addClass("select2-selected"))}),r.each2(function(t,e){e.is(".select2-result-selectable")||0!==e.find(".select2-result-selectable:not(.select2-selected)").length||e.addClass("select2-selected")}),-1==this.highlight()&&!1!==n&&!0===this.opts.closeOnSelect&&o.highlight(0),!this.opts.createSearchChoice&&!s.filter(".select2-result:not(.select2-selected)").length>0&&(!t||t&&!t.more&&0===this.results.find(".select2-no-results").length)&&A(o.opts.formatNoMatches,"formatNoMatches")&&this.results.append("<li class='select2-no-results'>"+k(o.opts.formatNoMatches,o.opts.element,o.search.val())+"</li>")},getMaxSearchWidth:function(){return this.selection.width()-y(this.search)},resizeSearch:function(){var e,n,i,s,r=y(this.search);e=function(e){if(!o){var n=e[0].currentStyle||window.getComputedStyle(e[0],null);(o=t(document.createElement("div")).css({position:"absolute",left:"-10000px",top:"-10000px",display:"none",fontSize:n.fontSize,fontFamily:n.fontFamily,fontStyle:n.fontStyle,fontWeight:n.fontWeight,letterSpacing:n.letterSpacing,textTransform:n.textTransform,whiteSpace:"nowrap"})).attr("class","select2-sizer"),t(document.body).append(o)}return o.text(e.val()),o.width()}(this.search)+10,n=this.search.offset().left,(s=(i=this.selection.width())-(n-this.selection.offset().left)-r)<e&&(s=i-r),s<40&&(s=i-r),s<=0&&(s=e),this.search.width(Math.floor(s))},getVal:function(){var t;return this.select?null===(t=this.select.val())?[]:t:b(t=this.opts.element.val(),this.opts.separator,this.opts.transformVal)},setVal:function(e){var n;this.select?this.select.val(e):(n=[],t(e).each(function(){m(this,n)<0&&n.push(this)}),this.opts.element.val(0===n.length?"":n.join(this.opts.separator)))},buildChangeDetails:function(t,e){e=e.slice(0),t=t.slice(0);for(var n=0;n<e.length;n++)for(var i=0;i<t.length;i++)v(this.opts.id(e[n]),this.opts.id(t[i]))&&(e.splice(n,1),n>0&&n--,t.splice(i,1),i--);return{added:e,removed:t}},val:function(n,i){var s,r=this;if(0===arguments.length)return this.getVal();if((s=this.data()).length||(s=[]),!n&&0!==n)return this.opts.element.val(""),this.updateSelection([]),this.clearSearch(),void(i&&this.triggerChange({added:this.data(),removed:s}));if(this.setVal(n),this.select)this.opts.initSelection(this.select,this.bind(this.updateSelection)),i&&this.triggerChange(this.buildChangeDetails(s,this.data()));else{if(this.opts.initSelection===e)throw new Error("val() cannot be called if initSelection() is not defined");this.opts.initSelection(this.opts.element,function(e){var n=t.map(e,r.id);r.setVal(n),r.updateSelection(e),r.clearSearch(),i&&r.triggerChange(r.buildChangeDetails(s,r.data()))})}this.clearSearch()},onSortStart:function(){if(this.select)throw new Error("Sorting of elements is not supported when attached to <select>. Attach to <input type='hidden'/> instead.");this.search.width(0),this.searchContainer.hide()},onSortEnd:function(){var e=[],n=this;this.searchContainer.show(),this.searchContainer.appendTo(this.searchContainer.parent()),this.resizeSearch(),this.selection.find(".select2-search-choice").each(function(){e.push(n.opts.id(t(this).data("select2-data")))}),this.setVal(e),this.triggerChange()},data:function(e,n){var i,s,r=this;if(0===arguments.length)return this.selection.children(".select2-search-choice").map(function(){return t(this).data("select2-data")}).get();s=this.data(),e||(e=[]),i=t.map(e,function(t){return r.opts.id(t)}),this.setVal(i),this.updateSelection(e),this.clearSearch(),n&&this.triggerChange(this.buildChangeDetails(s,this.data()))}}),t.fn.select2=function(){var n,i,s,r,o,a=Array.prototype.slice.call(arguments,0),l=["val","destroy","opened","open","close","focus","isFocused","container","dropdown","onSortStart","onSortEnd","enable","disable","readonly","positionDropdown","data","search"],c=["opened","isFocused","container","dropdown"],h=["val","data"],u={search:"externalSearch"};return this.each(function(){if(0===a.length||"object"==typeof a[0])(n=0===a.length?{}:t.extend({},a[0])).element=t(this),"select"===n.element.get(0).tagName.toLowerCase()?o=n.element.prop("multiple"):(o=n.multiple||!1,"tags"in n&&(n.multiple=o=!0)),(i=o?new window.Select2.class.multi:new window.Select2.class.single).init(n);else{if("string"!=typeof a[0])throw"Invalid arguments to select2 plugin: "+a;if(m(a[0],l)<0)throw"Unknown method: "+a[0];if(r=e,(i=t(this).data("select2"))===e)return;if("container"===(s=a[0])?r=i.container:"dropdown"===s?r=i.dropdown:(u[s]&&(s=u[s]),r=i[s].apply(i,a.slice(1))),m(a[0],c)>=0||m(a[0],h)>=0&&1==a.length)return!1}}),r===e?this:r},t.fn.select2.defaults={width:"copy",loadMorePadding:0,closeOnSelect:!0,openOnEnter:!0,containerCss:{},dropdownCss:{},containerCssClass:"",dropdownCssClass:"",formatResult:function(t,e,n,i){var s=[];return x(this.text(t),n.term,s,i),s.join("")},transformVal:function(e){return t.trim(e)},formatSelection:function(t,n,i){return t?i(this.text(t)):e},sortResults:function(t,e,n){return t},formatResultCssClass:function(t){return t.css},formatSelectionCssClass:function(t,n){return e},minimumResultsForSearch:0,minimumInputLength:0,maximumInputLength:null,maximumSelectionSize:0,id:function(t){return t==e?null:t.id},text:function(e){return e&&this.data&&this.data.text?t.isFunction(this.data.text)?this.data.text(e):e[this.data.text]:e.text},matcher:function(t,e){return g(""+e).toUpperCase().indexOf(g(""+t).toUpperCase())>=0},separator:",",tokenSeparators:[],tokenizer:function(t,n,i,s){var r,o,a,l,c,h=t,u=!1;if(!s.createSearchChoice||!s.tokenSeparators||s.tokenSeparators.length<1)return e;for(;;){for(o=-1,a=0,l=s.tokenSeparators.length;a<l&&(c=s.tokenSeparators[a],!((o=t.indexOf(c))>=0));a++);if(o<0)break;if(r=t.substring(0,o),t=t.substring(o+c.length),r.length>0&&(r=s.createSearchChoice.call(this,r,n))!==e&&null!==r&&s.id(r)!==e&&null!==s.id(r)){for(u=!1,a=0,l=n.length;a<l;a++)if(v(s.id(r),s.id(n[a]))){u=!0;break}u||i(r)}}return h!==t?t:void 0},escapeMarkup:T,blurOnChange:!1,selectOnBlur:!1,adaptContainerCssClass:function(t){return t},adaptDropdownCssClass:function(t){return null},nextSearchTerm:function(t,n){return e},searchInputPlaceholder:"",createSearchChoicePosition:"top",shouldFocusInput:function(t){return!("ontouchstart"in window||navigator.msMaxTouchPoints>0)||!(t.opts.minimumResultsForSearch<0)}},t.fn.select2.locales=[],t.fn.select2.locales.en={formatMatches:function(t){return 1===t?"One result is available, press enter to select it.":t+" results are available, use up and down arrow keys to navigate."},formatNoMatches:function(){return"No matches found"},formatAjaxError:function(t,e,n){return"Loading failed"},formatInputTooShort:function(t,e){var n=e-t.length;return"Please enter "+n+" or more character"+(1==n?"":"s")},formatInputTooLong:function(t,e){var n=t.length-e;return"Please delete "+n+" character"+(1==n?"":"s")},formatSelectionTooBig:function(t){return"You can only select "+t+" item"+(1==t?"":"s")},formatLoadMore:function(t){return"Loading more results…"},formatSearching:function(){return"Searching…"}},t.extend(t.fn.select2.defaults,t.fn.select2.locales.en),t.fn.select2.ajaxDefaults={transport:t.ajax,params:{type:"GET",cache:!1,dataType:"json"}},window.Select2={query:{ajax:D,local:E,tags:I},util:{debounce:w,markMatch:x,escapeMarkup:T,stripDiacritics:g},class:{abstract:n,single:i,multi:s}}}function p(e){var n=t(document.createTextNode(""));e.before(n),n.before(e),n.remove()}function g(t){return t.replace(/[^\u0000-\u007E]/g,function(t){return f[t]||t})}function m(t,e){for(var n=0,i=e.length;n<i;n+=1)if(v(t,e[n]))return n;return-1}function v(t,n){return t===n||t!==e&&n!==e&&(null!==t&&null!==n&&(t.constructor===String?t+""==n+"":n.constructor===String&&n+""==t+""))}function b(t,e,n){var i,s,r;if(null===t||t.length<1)return[];for(s=0,r=(i=t.split(e)).length;s<r;s+=1)i[s]=n(i[s]);return i}function y(t){return t.outerWidth(!1)-t.width()}function _(n){var i="keyup-change-value";n.on("keydown",function(){t.data(n,i)===e&&t.data(n,i,n.val())}),n.on("keyup",function(){var s=t.data(n,i);s!==e&&n.val()!==s&&(t.removeData(n,i),n.trigger("keyup-change"))})}function w(t,n,i){var s;return i=i||e,function(){var e=arguments;window.clearTimeout(s),s=window.setTimeout(function(){n.apply(i,e)},t)}}function C(t){t.preventDefault(),t.stopPropagation()}function S(e,n,i){var s,r,o=[];(s=t.trim(e.attr("class")))&&t((s=""+s).split(/\s+/)).each2(function(){0===this.indexOf("select2-")&&o.push(this)}),(s=t.trim(n.attr("class")))&&t((s=""+s).split(/\s+/)).each2(function(){0!==this.indexOf("select2-")&&(r=i(this))&&o.push(r)}),e.attr("class",o.join(" "))}function x(t,e,n,i){var s=g(t.toUpperCase()).indexOf(g(e.toUpperCase())),r=e.length;s<0?n.push(i(t)):(n.push(i(t.substring(0,s))),n.push("<span class='select2-match'>"),n.push(i(t.substring(s,s+r))),n.push("</span>"),n.push(i(t.substring(s+r,t.length))))}function T(t){var e={"\\":"&#92;","&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#47;"};return String(t).replace(/[&<>"'\/\\]/g,function(t){return e[t]})}function D(n){var i,s=null,r=n.quietMillis||100,o=n.url,a=this;return function(l){window.clearTimeout(i),i=window.setTimeout(function(){var i=n.data,r=o,c=n.transport||t.fn.select2.ajaxDefaults.transport,h={type:n.type||"GET",cache:n.cache||!1,jsonpCallback:n.jsonpCallback||e,dataType:n.dataType||"json"},u=t.extend({},t.fn.select2.ajaxDefaults.params,h);i=i?i.call(a,l.term,l.page,l.context):null,r="function"==typeof r?r.call(a,l.term,l.page,l.context):r,s&&"function"==typeof s.abort&&s.abort(),n.params&&(t.isFunction(n.params)?t.extend(u,n.params.call(a)):t.extend(u,n.params)),t.extend(u,{url:r,dataType:n.dataType,data:i,success:function(t){var e=n.results(t,l.page,l);l.callback(e)},error:function(t,e,n){var i={hasError:!0,jqXHR:t,textStatus:e,errorThrown:n};l.callback(i)}}),s=c.call(a,u)},r)}}function E(e){var n,i,s=e,r=function(t){return""+t.text};t.isArray(s)&&(s={results:i=s}),!1===t.isFunction(s)&&(i=s,s=function(){return i});var o=s();return o.text&&(r=o.text,t.isFunction(r)||(n=o.text,r=function(t){return t[n]})),function(e){var n,i=e.term,o={results:[]};""!==i?(n=function(s,o){var a,l;if((s=s[0]).children){for(l in a={},s)s.hasOwnProperty(l)&&(a[l]=s[l]);a.children=[],t(s.children).each2(function(t,e){n(e,a.children)}),(a.children.length||e.matcher(i,r(a),s))&&o.push(a)}else e.matcher(i,r(s),s)&&o.push(s)},t(s().results).each2(function(t,e){n(e,o.results)}),e.callback(o)):e.callback(s())}}function I(n){var i=t.isFunction(n);return function(s){var r=s.term,o={results:[]},a=i?n(s):n;t.isArray(a)&&(t(a).each(function(){var t=this.text!==e,n=t?this.text:this;(""===r||s.matcher(r,n))&&o.results.push(t?this:{id:this,text:this})}),s.callback(o))}}function A(e,n){if(t.isFunction(e))return!0;if(!e)return!1;if("string"==typeof e)return!0;throw new Error(n+" must be a string, function, or falsy value")}function k(e,n){if(t.isFunction(e)){var i=Array.prototype.slice.call(arguments,2);return e.apply(n,i)}return e}function O(){var e=this;t.each(arguments,function(t,n){e[n].remove(),e[n]=null})}function N(e,n){var i=function(){};return(i.prototype=new e).constructor=i,i.prototype.parent=e.prototype,i.prototype=t.extend(i.prototype,n),i}}(jQuery),function(t,e,n){!function(t){"use strict";"function"==typeof define&&define.amd?define("datatables",["jquery"],t):"object"==typeof exports?t(require("jquery")):jQuery&&!jQuery.fn.dataTable&&t(jQuery)}(function(i){"use strict";var s,r,o,a,l,c={},h=/[\r\n]/g,u=/<.*?>/g,d=/^[\w\+\-]/,f=/[\w\+\-]$/,p=new RegExp("(\\"+["/",".","*","+","?","|","(",")","[","]","{","}","\\","$","^","-"].join("|\\")+")","g"),g=/[',$£€¥%\u2009\u202F]/g,m=function(t){return!t||!0===t||"-"===t},v=function(t){var e=parseInt(t,10);return!isNaN(e)&&isFinite(t)?e:null},b=function(t,e){return c[e]||(c[e]=new RegExp(xt(e),"g")),"string"==typeof t&&"."!==e?t.replace(/\./g,"").replace(c[e],"."):t},y=function(t,e,n){var i="string"==typeof t;return e&&i&&(t=b(t,e)),n&&i&&(t=t.replace(g,"")),m(t)||!isNaN(parseFloat(t))&&isFinite(t)},_=function(t,e,n){return!!m(t)||(function(t){return m(t)||"string"==typeof t}(t)&&!!y(T(t),e,n)||null)},w=function(t,e,i){var s=[],r=0,o=t.length;if(i!==n)for(;r<o;r++)t[r]&&t[r][e]&&s.push(t[r][e][i]);else for(;r<o;r++)t[r]&&s.push(t[r][e]);return s},C=function(t,e,i,s){var r=[],o=0,a=e.length;if(s!==n)for(;o<a;o++)t[e[o]][i]&&r.push(t[e[o]][i][s]);else for(;o<a;o++)r.push(t[e[o]][i]);return r},S=function(t,e){var i,s=[];e===n?(e=0,i=t):(i=e,e=t);for(var r=e;r<i;r++)s.push(r);return s},x=function(t){for(var e=[],n=0,i=t.length;n<i;n++)t[n]&&e.push(t[n]);return e},T=function(t){return t.replace(u,"")},D=function(t){var e,n,i,s=[],r=t.length,o=0;t:for(n=0;n<r;n++){for(e=t[n],i=0;i<o;i++)if(s[i]===e)continue t;s.push(e),o++}return s};function E(t){var e,n,s={};i.each(t,function(i,r){(e=i.match(/^([^A-Z]+?)([A-Z])/))&&-1!=="a aa ai ao as b fn i m o s ".indexOf(e[1]+" ")&&(n=i.replace(e[0],e[2].toLowerCase()),s[n]=i,"o"===e[1]&&E(t[i]))}),t._hungarianMap=s}function I(t,e,s){var r;t._hungarianMap||E(t),i.each(e,function(o,a){(r=t._hungarianMap[o])===n||!s&&e[r]!==n||("o"===r.charAt(0)?(e[r]||(e[r]={}),i.extend(!0,e[r],e[o]),I(t[r],e[r],s)):e[r]=e[o])})}function A(t){var e=s.defaults.oLanguage,n=t.sZeroRecords;!t.sEmptyTable&&n&&"No data available in table"===e.sEmptyTable&&he(t,t,"sZeroRecords","sEmptyTable"),!t.sLoadingRecords&&n&&"Loading..."===e.sLoadingRecords&&he(t,t,"sZeroRecords","sLoadingRecords"),t.sInfoThousands&&(t.sThousands=t.sInfoThousands);var i=t.sDecimal;i&&Pe(i)}var k=function(t,e,i){t[e]!==n&&(t[i]=t[e])};function O(t){k(t,"ordering","bSort"),k(t,"orderMulti","bSortMulti"),k(t,"orderClasses","bSortClasses"),k(t,"orderCellsTop","bSortCellsTop"),k(t,"order","aaSorting"),k(t,"orderFixed","aaSortingFixed"),k(t,"paging","bPaginate"),k(t,"pagingType","sPaginationType"),k(t,"pageLength","iDisplayLength"),k(t,"searching","bFilter");var e=t.aoSearchCols;if(e)for(var n=0,i=e.length;n<i;n++)e[n]&&I(s.models.oSearch,e[n])}function N(t){k(t,"orderable","bSortable"),k(t,"orderData","aDataSort"),k(t,"orderSequence","asSorting"),k(t,"orderDataType","sortDataType")}function P(t){var e=t.oBrowser,n=i("<div/>").css({position:"absolute",top:0,left:0,height:1,width:1,overflow:"hidden"}).append(i("<div/>").css({position:"absolute",top:1,left:1,width:100,overflow:"scroll"}).append(i('<div class="test"/>').css({width:"100%",height:10}))).appendTo("body"),s=n.find(".test");e.bScrollOversize=100===s[0].offsetWidth,e.bScrollbarLeft=1!==s.offset().left,n.remove()}function L(t,e,i,s,r,o){var a,l=s,c=!1;for(i!==n&&(a=i,c=!0);l!==r;)t.hasOwnProperty(l)&&(a=c?e(a,t[l],l,t):t[l],c=!0,l+=o);return a}function R(t,n){var r=s.defaults.column,o=t.aoColumns.length,a=i.extend({},s.models.oColumn,r,{nTh:n||e.createElement("th"),sTitle:r.sTitle?r.sTitle:n?n.innerHTML:"",aDataSort:r.aDataSort?r.aDataSort:[o],mData:r.mData?r.mData:o,idx:o});t.aoColumns.push(a);var l=t.aoPreSearchCols;l[o]=i.extend({},s.models.oSearch,l[o]),F(t,o,null)}function F(t,e,r){var o=t.aoColumns[e],a=t.oClasses,l=i(o.nTh);if(!o.sWidthOrig){o.sWidthOrig=l.attr("width")||null;var c=(l.attr("style")||"").match(/width:\s*(\d+[pxem%]+)/);c&&(o.sWidthOrig=c[1])}r!==n&&null!==r&&(N(r),I(s.defaults.column,r),r.mDataProp===n||r.mData||(r.mData=r.mDataProp),r.sType&&(o._sManualType=r.sType),r.className&&!r.sClass&&(r.sClass=r.className),i.extend(o,r),he(o,r,"sWidth","sWidthOrig"),"number"==typeof r.iDataSort&&(o.aDataSort=[r.iDataSort]),he(o,r,"aDataSort"));var h=o.mData,u=J(h),d=o.mRender?J(o.mRender):null,f=function(t){return"string"==typeof t&&-1!==t.indexOf("@")};o._bAttrSrc=i.isPlainObject(h)&&(f(h.sort)||f(h.type)||f(h.filter)),o.fnGetData=function(t,e,i){var s=u(t,e,n,i);return d&&e?d(s,e,t,i):s},o.fnSetData=function(t,e,n){return X(h)(t,e,n)},"number"!=typeof h&&(t._rowReadObject=!0),t.oFeatures.bSort||(o.bSortable=!1,l.addClass(a.sSortableNone));var p=-1!==i.inArray("asc",o.asSorting),g=-1!==i.inArray("desc",o.asSorting);o.bSortable&&(p||g)?p&&!g?(o.sSortingClass=a.sSortableAsc,o.sSortingClassJUI=a.sSortJUIAscAllowed):!p&&g?(o.sSortingClass=a.sSortableDesc,o.sSortingClassJUI=a.sSortJUIDescAllowed):(o.sSortingClass=a.sSortable,o.sSortingClassJUI=a.sSortJUI):(o.sSortingClass=a.sSortableNone,o.sSortingClassJUI="")}function j(t){if(!1!==t.oFeatures.bAutoWidth){var e=t.aoColumns;zt(t);for(var n=0,i=e.length;n<i;n++)e[n].nTh.style.width=e[n].sWidth}var s=t.oScroll;""===s.sY&&""===s.sX||Ut(t),pe(t,null,"column-sizing",[t])}function M(t,e){var n=W(t,"bVisible");return"number"==typeof n[e]?n[e]:null}function H(t,e){var n=W(t,"bVisible"),s=i.inArray(e,n);return-1!==s?s:null}function $(t){return W(t,"bVisible").length}function W(t,e){var n=[];return i.map(t.aoColumns,function(t,i){t[e]&&n.push(i)}),n}function U(t){var e,i,r,o,a,l,c,h,u,d=t.aoColumns,f=t.aoData,p=s.ext.type.detect;for(e=0,i=d.length;e<i;e++)if(u=[],!(c=d[e]).sType&&c._sManualType)c.sType=c._sManualType;else if(!c.sType){for(r=0,o=p.length;r<o;r++){for(a=0,l=f.length;a<l&&(u[a]===n&&(u[a]=K(t,a,e,"type")),(h=p[r](u[a],t))||r===p.length-1)&&"html"!==h;a++);if(h){c.sType=h;break}}c.sType||(c.sType="string")}}function V(t,e,s,r){var o,a,l,c,h,u,d,f=t.aoColumns;if(e)for(o=e.length-1;o>=0;o--){var p=(d=e[o]).targets!==n?d.targets:d.aTargets;for(i.isArray(p)||(p=[p]),l=0,c=p.length;l<c;l++)if("number"==typeof p[l]&&p[l]>=0){for(;f.length<=p[l];)R(t);r(p[l],d)}else if("number"==typeof p[l]&&p[l]<0)r(f.length+p[l],d);else if("string"==typeof p[l])for(h=0,u=f.length;h<u;h++)("_all"==p[l]||i(f[h].nTh).hasClass(p[l]))&&r(h,d)}if(s)for(o=0,a=s.length;o<a;o++)r(o,s[o])}function B(t,e,n,r){var o=t.aoData.length,a=i.extend(!0,{},s.models.oRow,{src:n?"dom":"data"});a._aData=e,t.aoData.push(a);for(var l=t.aoColumns,c=0,h=l.length;c<h;c++)n&&q(t,o,c,K(t,o,c)),l[c].sType=null;return t.aiDisplayMaster.push(o),!n&&t.oFeatures.bDeferRender||st(t,o,n,r),o}function z(t,e){var n;return e instanceof i||(e=i(e)),e.map(function(e,i){return n=it(t,i),B(t,n.data,i,n.cells)})}function K(t,e,i,s){var r=t.iDraw,o=t.aoColumns[i],a=t.aoData[e]._aData,l=o.sDefaultContent,c=o.fnGetData(a,s,{settings:t,row:e,col:i});if(c===n)return t.iDrawError!=r&&null===l&&(ce(t,0,"Requested unknown parameter "+("function"==typeof o.mData?"{function}":"'"+o.mData+"'")+" for row "+e,4),t.iDrawError=r),l;if(c!==a&&null!==c||null===l){if("function"==typeof c)return c.call(a)}else c=l;return null===c&&"display"==s?"":c}function q(t,e,n,i){var s=t.aoColumns[n],r=t.aoData[e]._aData;s.fnSetData(r,i,{settings:t,row:e,col:n})}var G=/\[.*?\]$/,Q=/\(\)$/;function Y(t){return i.map(t.match(/(\\.|[^\.])+/g),function(t){return t.replace(/\\./g,".")})}function J(t){if(i.isPlainObject(t)){var e={};return i.each(t,function(t,n){n&&(e[t]=J(n))}),function(t,i,s,r){var o=e[i]||e._;return o!==n?o(t,i,s,r):t}}if(null===t)return function(t){return t};if("function"==typeof t)return function(e,n,i,s){return t(e,n,i,s)};if("string"!=typeof t||-1===t.indexOf(".")&&-1===t.indexOf("[")&&-1===t.indexOf("("))return function(e,n){return e[t]};var s=function(t,e,i){var r,o,a,l;if(""!==i)for(var c=Y(i),h=0,u=c.length;h<u;h++){if(r=c[h].match(G),o=c[h].match(Q),r){c[h]=c[h].replace(G,""),""!==c[h]&&(t=t[c[h]]),a=[],c.splice(0,h+1),l=c.join(".");for(var d=0,f=t.length;d<f;d++)a.push(s(t[d],e,l));var p=r[0].substring(1,r[0].length-1);t=""===p?a:a.join(p);break}if(o)c[h]=c[h].replace(Q,""),t=t[c[h]]();else{if(null===t||t[c[h]]===n)return n;t=t[c[h]]}}return t};return function(e,n){return s(e,n,t)}}function X(t){if(i.isPlainObject(t))return X(t._);if(null===t)return function(){};if("function"==typeof t)return function(e,n,i){t(e,"set",n,i)};if("string"!=typeof t||-1===t.indexOf(".")&&-1===t.indexOf("[")&&-1===t.indexOf("("))return function(e,n){e[t]=n};var e=function(t,i,s){for(var r,o,a,l,c,h=Y(s),u=h[h.length-1],d=0,f=h.length-1;d<f;d++){if(o=h[d].match(G),a=h[d].match(Q),o){h[d]=h[d].replace(G,""),t[h[d]]=[],(r=h.slice()).splice(0,d+1),c=r.join(".");for(var p=0,g=i.length;p<g;p++)e(l={},i[p],c),t[h[d]].push(l);return}a&&(h[d]=h[d].replace(Q,""),t=t[h[d]](i)),null!==t[h[d]]&&t[h[d]]!==n||(t[h[d]]={}),t=t[h[d]]}u.match(Q)?t=t[u.replace(Q,"")](i):t[u.replace(G,"")]=i};return function(n,i){return e(n,i,t)}}function Z(t){return w(t.aoData,"_aData")}function tt(t){t.aoData.length=0,t.aiDisplayMaster.length=0,t.aiDisplay.length=0}function et(t,e,i){for(var s=-1,r=0,o=t.length;r<o;r++)t[r]==e?s=r:t[r]>e&&t[r]--;-1!=s&&i===n&&t.splice(s,1)}function nt(t,e,i,s){var r,o,a=t.aoData[e],l=function(n,i){for(;n.childNodes.length;)n.removeChild(n.firstChild);n.innerHTML=K(t,e,i,"display")};if("dom"!==i&&(i&&"auto"!==i||"dom"!==a.src)){var c=a.anCells;if(c)if(s!==n)l(c[s],s);else for(r=0,o=c.length;r<o;r++)l(c[r],r)}else a._aData=it(t,a,s,s===n?n:a._aData).data;a._aSortData=null,a._aFilterData=null;var h=t.aoColumns;if(s!==n)h[s].sType=null;else{for(r=0,o=h.length;r<o;r++)h[r].sType=null;rt(a)}}function it(t,e,s,r){var o,a,l,c=[],h=e.firstChild,u=0,d=t.aoColumns,f=t._rowReadObject;r=r||f?{}:[];var p=function(t,e){if("string"==typeof t){var n=t.indexOf("@");if(-1!==n){var i=t.substring(n+1);X(t)(r,e.getAttribute(i))}}},g=function(t){s!==n&&s!==u||(a=d[u],l=i.trim(t.innerHTML),a&&a._bAttrSrc?(X(a.mData._)(r,l),p(a.mData.sort,t),p(a.mData.type,t),p(a.mData.filter,t)):f?(a._setter||(a._setter=X(a.mData)),a._setter(r,l)):r[u]=l);u++};if(h)for(;h;)"TD"!=(o=h.nodeName.toUpperCase())&&"TH"!=o||(g(h),c.push(h)),h=h.nextSibling;else for(var m=0,v=(c=e.anCells).length;m<v;m++)g(c[m]);return{data:r,cells:c}}function st(t,n,i,s){var r,o,a,l,c,h=t.aoData[n],u=h._aData,d=[];if(null===h.nTr){for(r=i||e.createElement("tr"),h.nTr=r,h.anCells=d,r._DT_RowIndex=n,rt(h),l=0,c=t.aoColumns.length;l<c;l++)a=t.aoColumns[l],o=i?s[l]:e.createElement(a.sCellType),d.push(o),i&&!a.mRender&&a.mData===l||(o.innerHTML=K(t,n,l,"display")),a.sClass&&(o.className+=" "+a.sClass),a.bVisible&&!i?r.appendChild(o):!a.bVisible&&i&&o.parentNode.removeChild(o),a.fnCreatedCell&&a.fnCreatedCell.call(t.oInstance,o,K(t,n,l),u,n,l);pe(t,"aoRowCreatedCallback",null,[r,u,n])}h.nTr.setAttribute("role","row")}function rt(t){var e=t.nTr,n=t._aData;if(e){if(n.DT_RowId&&(e.id=n.DT_RowId),n.DT_RowClass){var s=n.DT_RowClass.split(" ");t.__rowc=t.__rowc?D(t.__rowc.concat(s)):s,i(e).removeClass(t.__rowc.join(" ")).addClass(n.DT_RowClass)}n.DT_RowData&&i(e).data(n.DT_RowData)}}function ot(t){var e,n,s,r,o,a=t.nTHead,l=t.nTFoot,c=0===i("th, td",a).length,h=t.oClasses,u=t.aoColumns;for(c&&(r=i("<tr/>").appendTo(a)),e=0,n=u.length;e<n;e++)o=u[e],s=i(o.nTh).addClass(o.sClass),c&&s.appendTo(r),t.oFeatures.bSort&&(s.addClass(o.sSortingClass),!1!==o.bSortable&&(s.attr("tabindex",t.iTabIndex).attr("aria-controls",t.sTableId),ie(t,o.nTh,e))),o.sTitle!=s.html()&&s.html(o.sTitle),me(t,"header")(t,s,o,h);if(c&&ut(t.aoHeader,a),i(a).find(">tr").attr("role","row"),i(a).find(">tr>th, >tr>td").addClass(h.sHeaderTH),i(l).find(">tr>th, >tr>td").addClass(h.sFooterTH),null!==l){var d=t.aoFooter[0];for(e=0,n=d.length;e<n;e++)(o=u[e]).nTf=d[e].cell,o.sClass&&i(o.nTf).addClass(o.sClass)}}function at(t,e,s){var r,o,a,l,c,h,u,d,f,p=[],g=[],m=t.aoColumns.length;if(e){for(s===n&&(s=!1),r=0,o=e.length;r<o;r++){for(p[r]=e[r].slice(),p[r].nTr=e[r].nTr,a=m-1;a>=0;a--)t.aoColumns[a].bVisible||s||p[r].splice(a,1);g.push([])}for(r=0,o=p.length;r<o;r++){if(u=p[r].nTr)for(;h=u.firstChild;)u.removeChild(h);for(a=0,l=p[r].length;a<l;a++)if(d=1,f=1,g[r][a]===n){for(u.appendChild(p[r][a].cell),g[r][a]=1;p[r+d]!==n&&p[r][a].cell==p[r+d][a].cell;)g[r+d][a]=1,d++;for(;p[r][a+f]!==n&&p[r][a].cell==p[r][a+f].cell;){for(c=0;c<d;c++)g[r+c][a+f]=1;f++}i(p[r][a].cell).attr("rowspan",d).attr("colspan",f)}}}}function lt(t){var e=pe(t,"aoPreDrawCallback","preDraw",[t]);if(-1===i.inArray(!1,e)){var s=[],r=0,o=t.asStripeClasses,a=o.length,l=(t.aoOpenRows.length,t.oLanguage),c=t.iInitDisplayStart,h="ssp"==ve(t),u=t.aiDisplay;t.bDrawing=!0,c!==n&&-1!==c&&(t._iDisplayStart=h?c:c>=t.fnRecordsDisplay()?0:c,t.iInitDisplayStart=-1);var d=t._iDisplayStart,f=t.fnDisplayEnd();if(t.bDeferLoading)t.bDeferLoading=!1,t.iDraw++,$t(t,!1);else if(h){if(!t.bDestroying&&!pt(t))return}else t.iDraw++;if(0!==u.length)for(var p=h?0:d,g=h?t.aoData.length:f,m=p;m<g;m++){var v=u[m],b=t.aoData[v];null===b.nTr&&st(t,v);var y=b.nTr;if(0!==a){var _=o[r%a];b._sRowStripe!=_&&(i(y).removeClass(b._sRowStripe).addClass(_),b._sRowStripe=_)}pe(t,"aoRowCallback",null,[y,b._aData,r,m]),s.push(y),r++}else{var w=l.sZeroRecords;1==t.iDraw&&"ajax"==ve(t)?w=l.sLoadingRecords:l.sEmptyTable&&0===t.fnRecordsTotal()&&(w=l.sEmptyTable),s[0]=i("<tr/>",{class:a?o[0]:""}).append(i("<td />",{valign:"top",colSpan:$(t),class:t.oClasses.sRowEmpty}).html(w))[0]}pe(t,"aoHeaderCallback","header",[i(t.nTHead).children("tr")[0],Z(t),d,f,u]),pe(t,"aoFooterCallback","footer",[i(t.nTFoot).children("tr")[0],Z(t),d,f,u]);var C=i(t.nTBody);C.children().detach(),C.append(i(s)),pe(t,"aoDrawCallback","draw",[t]),t.bSorted=!1,t.bFiltered=!1,t.bDrawing=!1}else $t(t,!1)}function ct(t,e){var n=t.oFeatures,i=n.bSort,s=n.bFilter;i&&te(t),s?yt(t,t.oPreviousSearch):t.aiDisplay=t.aiDisplayMaster.slice(),!0!==e&&(t._iDisplayStart=0),t._drawHold=e,lt(t),t._drawHold=!1}function ht(t){var e=t.oClasses,n=i(t.nTable),r=i("<div/>").insertBefore(n),o=t.oFeatures,a=i("<div/>",{id:t.sTableId+"_wrapper",class:e.sWrapper+(t.nTFoot?"":" "+e.sNoFooter)});t.nHolding=r[0],t.nTableWrapper=a[0],t.nTableReinsertBefore=t.nTable.nextSibling;for(var l,c,h,u,d,f,p=t.sDom.split(""),g=0;g<p.length;g++){if(l=null,"<"==(c=p[g])){if(h=i("<div/>")[0],"'"==(u=p[g+1])||'"'==u){for(d="",f=2;p[g+f]!=u;)d+=p[g+f],f++;if("H"==d?d=e.sJUIHeader:"F"==d&&(d=e.sJUIFooter),-1!=d.indexOf(".")){var m=d.split(".");h.id=m[0].substr(1,m[0].length-1),h.className=m[1]}else"#"==d.charAt(0)?h.id=d.substr(1,d.length-1):h.className=d;g+=f}a.append(h),a=i(h)}else if(">"==c)a=a.parent();else if("l"==c&&o.bPaginate&&o.bLengthChange)l=Ft(t);else if("f"==c&&o.bFilter)l=bt(t);else if("r"==c&&o.bProcessing)l=Ht(t);else if("t"==c)l=Wt(t);else if("i"==c&&o.bInfo)l=kt(t);else if("p"==c&&o.bPaginate)l=jt(t);else if(0!==s.ext.feature.length)for(var v=s.ext.feature,b=0,y=v.length;b<y;b++)if(c==v[b].cFeature){l=v[b].fnInit(t);break}if(l){var _=t.aanFeatures;_[c]||(_[c]=[]),_[c].push(l),a.append(l)}}r.replaceWith(a)}function ut(t,e){var n,s,r,o,a,l,c,h,u,d,f,p=i(e).children("tr"),g=function(t,e,n){for(var i=t[e];i[n];)n++;return n};for(t.splice(0,t.length),r=0,l=p.length;r<l;r++)t.push([]);for(r=0,l=p.length;r<l;r++)for(h=0,s=(n=p[r]).firstChild;s;){if("TD"==s.nodeName.toUpperCase()||"TH"==s.nodeName.toUpperCase())for(u=1*s.getAttribute("colspan"),d=1*s.getAttribute("rowspan"),u=u&&0!==u&&1!==u?u:1,d=d&&0!==d&&1!==d?d:1,c=g(t,r,h),f=1===u,a=0;a<u;a++)for(o=0;o<d;o++)t[r+o][c+a]={cell:s,unique:f},t[r+o].nTr=n;s=s.nextSibling}}function dt(t,e,n){var i=[];n||(n=t.aoHeader,e&&ut(n=[],e));for(var s=0,r=n.length;s<r;s++)for(var o=0,a=n[s].length;o<a;o++)!n[s][o].unique||i[o]&&t.bSortCellsTop||(i[o]=n[s][o].cell);return i}function ft(t,e,n){if(pe(t,"aoServerParams","serverParams",[e]),e&&i.isArray(e)){var s={},r=/(.*?)\[\]$/;i.each(e,function(t,e){var n=e.name.match(r);if(n){var i=n[0];s[i]||(s[i]=[]),s[i].push(e.value)}else s[e.name]=e.value}),e=s}var o,a=t.ajax,l=t.oInstance;if(i.isPlainObject(a)&&a.data){o=a.data;var c=i.isFunction(o)?o(e):o;e=i.isFunction(o)&&c?c:i.extend(!0,e,c),delete a.data}var h={data:e,success:function(e){var i=e.error||e.sError;i&&t.oApi._fnLog(t,0,i),t.json=e,pe(t,null,"xhr",[t,e]),n(e)},dataType:"json",cache:!1,type:t.sServerMethod,error:function(e,n,i){var s=t.oApi._fnLog;"parsererror"==n?s(t,0,"Invalid JSON response",1):4===e.readyState&&s(t,0,"Ajax error",7),$t(t,!1)}};t.oAjaxData=e,pe(t,null,"preXhr",[t,e]),t.fnServerData?t.fnServerData.call(l,t.sAjaxSource,i.map(e,function(t,e){return{name:e,value:t}}),n,t):t.sAjaxSource||"string"==typeof a?t.jqXHR=i.ajax(i.extend(h,{url:a||t.sAjaxSource})):i.isFunction(a)?t.jqXHR=a.call(l,e,n,t):(t.jqXHR=i.ajax(i.extend(h,a)),a.data=o)}function pt(t){return!t.bAjaxDataGet||(t.iDraw++,$t(t,!0),ft(t,gt(t),function(e){mt(t,e)}),!1)}function gt(t){var e,n,r,o,a=t.aoColumns,l=a.length,c=t.oFeatures,h=t.oPreviousSearch,u=t.aoPreSearchCols,d=[],f=Zt(t),p=t._iDisplayStart,g=!1!==c.bPaginate?t._iDisplayLength:-1,m=function(t,e){d.push({name:t,value:e})};m("sEcho",t.iDraw),m("iColumns",l),m("sColumns",w(a,"sName").join(",")),m("iDisplayStart",p),m("iDisplayLength",g);var v={draw:t.iDraw,columns:[],order:[],start:p,length:g,search:{value:h.sSearch,regex:h.bRegex}};for(e=0;e<l;e++)r=a[e],o=u[e],n="function"==typeof r.mData?"function":r.mData,v.columns.push({data:n,name:r.sName,searchable:r.bSearchable,orderable:r.bSortable,search:{value:o.sSearch,regex:o.bRegex}}),m("mDataProp_"+e,n),c.bFilter&&(m("sSearch_"+e,o.sSearch),m("bRegex_"+e,o.bRegex),m("bSearchable_"+e,r.bSearchable)),c.bSort&&m("bSortable_"+e,r.bSortable);c.bFilter&&(m("sSearch",h.sSearch),m("bRegex",h.bRegex)),c.bSort&&(i.each(f,function(t,e){v.order.push({column:e.col,dir:e.dir}),m("iSortCol_"+t,e.col),m("sSortDir_"+t,e.dir)}),m("iSortingCols",f.length));var b=s.ext.legacy.ajax;return null===b?t.sAjaxSource?d:v:b?d:v}function mt(t,e){var i=function(t,i){return e[t]!==n?e[t]:e[i]},s=i("sEcho","draw"),r=i("iTotalRecords","recordsTotal"),o=i("iTotalDisplayRecords","recordsFiltered");if(s){if(1*s<t.iDraw)return;t.iDraw=1*s}tt(t),t._iRecordsTotal=parseInt(r,10),t._iRecordsDisplay=parseInt(o,10);for(var a=vt(t,e),l=0,c=a.length;l<c;l++)B(t,a[l]);t.aiDisplay=t.aiDisplayMaster.slice(),t.bAjaxDataGet=!1,lt(t),t._bInitComplete||Lt(t,e),t.bAjaxDataGet=!0,$t(t,!1)}function vt(t,e){var s=i.isPlainObject(t.ajax)&&t.ajax.dataSrc!==n?t.ajax.dataSrc:t.sAjaxDataProp;return"data"===s?e.aaData||e[s]:""!==s?J(s)(e):e}function bt(t){var n=t.oClasses,s=t.sTableId,r=t.oLanguage,o=t.oPreviousSearch,a=t.aanFeatures,l='<input type="search" class="'+n.sFilterInput+'"/>',c=r.sSearch;c=c.match(/_INPUT_/)?c.replace("_INPUT_",l):c+l;var h=i("<div/>",{id:a.f?null:s+"_filter",class:n.sFilter}).append(i("<label/>").append(c)),u=function(){a.f;var e=this.value?this.value:"";e!=o.sSearch&&(yt(t,{sSearch:e,bRegex:o.bRegex,bSmart:o.bSmart,bCaseInsensitive:o.bCaseInsensitive}),t._iDisplayStart=0,lt(t))},d=null!==t.searchDelay?t.searchDelay:"ssp"===ve(t)?400:0,f=i("input",h).val(o.sSearch).attr("placeholder",r.sSearchPlaceholder).bind("keyup.DT search.DT input.DT paste.DT cut.DT",d?Kt(u,d):u).bind("keypress.DT",function(t){if(13==t.keyCode)return!1}).attr("aria-controls",s);return i(t.nTable).on("search.dt.DT",function(n,i){if(t===i)try{f[0]!==e.activeElement&&f.val(o.sSearch)}catch(t){}}),h[0]}function yt(t,e,i){var s=t.oPreviousSearch,r=t.aoPreSearchCols,o=function(t){s.sSearch=t.sSearch,s.bRegex=t.bRegex,s.bSmart=t.bSmart,s.bCaseInsensitive=t.bCaseInsensitive},a=function(t){return t.bEscapeRegex!==n?!t.bEscapeRegex:t.bRegex};if(U(t),"ssp"!=ve(t)){Ct(t,e.sSearch,i,a(e),e.bSmart,e.bCaseInsensitive),o(e);for(var l=0;l<r.length;l++)wt(t,r[l].sSearch,l,a(r[l]),r[l].bSmart,r[l].bCaseInsensitive);_t(t)}else o(e);t.bFiltered=!0,pe(t,null,"search",[t])}function _t(t){for(var e,n,i=s.ext.search,r=t.aiDisplay,o=0,a=i.length;o<a;o++){for(var l=[],c=0,h=r.length;c<h;c++)n=r[c],e=t.aoData[n],i[o](t,e._aFilterData,n,e._aData,c)&&l.push(n);r.length=0,r.push.apply(r,l)}}function wt(t,e,n,i,s,r){if(""!==e)for(var o,a=t.aiDisplay,l=St(e,i,s,r),c=a.length-1;c>=0;c--)o=t.aoData[a[c]]._aFilterData[n],l.test(o)||a.splice(c,1)}function Ct(t,e,n,i,r,o){var a,l,c,h=St(e,i,r,o),u=t.oPreviousSearch.sSearch,d=t.aiDisplayMaster;if(0!==s.ext.search.length&&(n=!0),l=Et(t),e.length<=0)t.aiDisplay=d.slice();else for((l||n||u.length>e.length||0!==e.indexOf(u)||t.bSorted)&&(t.aiDisplay=d.slice()),c=(a=t.aiDisplay).length-1;c>=0;c--)h.test(t.aoData[a[c]]._sFilterRow)||a.splice(c,1)}function St(t,e,n,s){(t=e?t:xt(t),n)&&(t="^(?=.*?"+i.map(t.match(/"[^"]+"|[^ ]+/g)||"",function(t){if('"'===t.charAt(0)){var e=t.match(/^"(.*)"$/);t=e?e[1]:t}return t.replace('"',"")}).join(")(?=.*?")+").*$");return new RegExp(t,s?"i":"")}function xt(t){return t.replace(p,"\\$1")}var Tt=i("<div>")[0],Dt=Tt.textContent!==n;function Et(t){var e,n,i,r,o,a,l,c,h=t.aoColumns,u=s.ext.type.search,d=!1;for(n=0,r=t.aoData.length;n<r;n++)if(!(c=t.aoData[n])._aFilterData){for(a=[],i=0,o=h.length;i<o;i++)(e=h[i]).bSearchable?(l=K(t,n,i,"filter"),u[e.sType]&&(l=u[e.sType](l)),null===l&&(l=""),"string"!=typeof l&&l.toString&&(l=l.toString())):l="",l.indexOf&&-1!==l.indexOf("&")&&(Tt.innerHTML=l,l=Dt?Tt.textContent:Tt.innerText),l.replace&&(l=l.replace(/[\r\n]/g,"")),a.push(l);c._aFilterData=a,c._sFilterRow=a.join("  "),d=!0}return d}function It(t){return{search:t.sSearch,smart:t.bSmart,regex:t.bRegex,caseInsensitive:t.bCaseInsensitive}}function At(t){return{sSearch:t.search,bSmart:t.smart,bRegex:t.regex,bCaseInsensitive:t.caseInsensitive}}function kt(t){var e=t.sTableId,n=t.aanFeatures.i,s=i("<div/>",{class:t.oClasses.sInfo,id:n?null:e+"_info"});return n||(t.aoDrawCallback.push({fn:Ot,sName:"information"}),s.attr("role","status").attr("aria-live","polite"),i(t.nTable).attr("aria-describedby",e+"_info")),s[0]}function Ot(t){var e=t.aanFeatures.i;if(0!==e.length){var n=t.oLanguage,s=t._iDisplayStart+1,r=t.fnDisplayEnd(),o=t.fnRecordsTotal(),a=t.fnRecordsDisplay(),l=a?n.sInfo:n.sInfoEmpty;a!==o&&(l+=" "+n.sInfoFiltered),l=Nt(t,l+=n.sInfoPostFix);var c=n.fnInfoCallback;null!==c&&(l=c.call(t.oInstance,t,s,r,o,a,l)),i(e).html(l)}}function Nt(t,e){var n=t.fnFormatNumber,i=t._iDisplayStart+1,s=t._iDisplayLength,r=t.fnRecordsDisplay(),o=-1===s;return e.replace(/_START_/g,n.call(t,i)).replace(/_END_/g,n.call(t,t.fnDisplayEnd())).replace(/_MAX_/g,n.call(t,t.fnRecordsTotal())).replace(/_TOTAL_/g,n.call(t,r)).replace(/_PAGE_/g,n.call(t,o?1:Math.ceil(i/s))).replace(/_PAGES_/g,n.call(t,o?1:Math.ceil(r/s)))}function Pt(t){var e,n,i,s=t.iInitDisplayStart,r=t.aoColumns,o=t.oFeatures;if(t.bInitialised){for(ht(t),ot(t),at(t,t.aoHeader),at(t,t.aoFooter),$t(t,!0),o.bAutoWidth&&zt(t),e=0,n=r.length;e<n;e++)(i=r[e]).sWidth&&(i.nTh.style.width=Jt(i.sWidth));ct(t);var a=ve(t);"ssp"!=a&&("ajax"==a?ft(t,[],function(n){var i=vt(t,n);for(e=0;e<i.length;e++)B(t,i[e]);t.iInitDisplayStart=s,ct(t),$t(t,!1),Lt(t,n)}):($t(t,!1),Lt(t)))}else setTimeout(function(){Pt(t)},200)}function Lt(t,e){t._bInitComplete=!0,e&&j(t),pe(t,"aoInitComplete","init",[t,e])}function Rt(t,e){var n=parseInt(e,10);t._iDisplayLength=n,ge(t),pe(t,null,"length",[t,n])}function Ft(t){for(var e=t.oClasses,n=t.sTableId,s=t.aLengthMenu,r=i.isArray(s[0]),o=r?s[0]:s,a=r?s[1]:s,l=i("<select/>",{name:n+"_length","aria-controls":n,class:e.sLengthSelect}),c=0,h=o.length;c<h;c++)l[0][c]=new Option(a[c],o[c]);var u=i("<div><label/></div>").addClass(e.sLength);return t.aanFeatures.l||(u[0].id=n+"_length"),u.children().append(t.oLanguage.sLengthMenu.replace("_MENU_",l[0].outerHTML)),i("select",u).val(t._iDisplayLength).bind("change.DT",function(e){Rt(t,i(this).val()),lt(t)}),i(t.nTable).bind("length.dt.DT",function(e,n,s){t===n&&i("select",u).val(s)}),u[0]}function jt(t){var e=t.sPaginationType,n=s.ext.pager[e],r="function"==typeof n,o=function(t){lt(t)},a=i("<div/>").addClass(t.oClasses.sPaging+e)[0],l=t.aanFeatures;return r||n.fnInit(t,a,o),l.p||(a.id=t.sTableId+"_paginate",t.aoDrawCallback.push({fn:function(t){if(r){var e,i,s=t._iDisplayStart,a=t._iDisplayLength,c=t.fnRecordsDisplay(),h=-1===a,u=h?0:Math.ceil(s/a),d=h?1:Math.ceil(c/a),f=n(u,d);for(e=0,i=l.p.length;e<i;e++)me(t,"pageButton")(t,l.p[e],e,f,u,d)}else n.fnUpdate(t,o)},sName:"pagination"})),a}function Mt(t,e,n){var i=t._iDisplayStart,s=t._iDisplayLength,r=t.fnRecordsDisplay();0===r||-1===s?i=0:"number"==typeof e?(i=e*s)>r&&(i=0):"first"==e?i=0:"previous"==e?(i=s>=0?i-s:0)<0&&(i=0):"next"==e?i+s<r&&(i+=s):"last"==e?i=Math.floor((r-1)/s)*s:ce(t,0,"Unknown paging action: "+e,5);var o=t._iDisplayStart!==i;return t._iDisplayStart=i,o&&(pe(t,null,"page",[t]),n&&lt(t)),o}function Ht(t){return i("<div/>",{id:t.aanFeatures.r?null:t.sTableId+"_processing",class:t.oClasses.sProcessing}).html(t.oLanguage.sProcessing).insertBefore(t.nTable)[0]}function $t(t,e){t.oFeatures.bProcessing&&i(t.aanFeatures.r).css("display",e?"block":"none"),pe(t,null,"processing",[t,e])}function Wt(t){var e=i(t.nTable);e.attr("role","grid");var n=t.oScroll;if(""===n.sX&&""===n.sY)return t.nTable;var s=n.sX,r=n.sY,o=t.oClasses,a=e.children("caption"),l=a.length?a[0]._captionSide:null,c=i(e[0].cloneNode(!1)),h=i(e[0].cloneNode(!1)),u=e.children("tfoot"),d="<div/>",f=function(t){return t?Jt(t):null};n.sX&&"100%"===e.attr("width")&&e.removeAttr("width"),u.length||(u=null);var p=i(d,{class:o.sScrollWrapper}).append(i(d,{class:o.sScrollHead}).css({overflow:"hidden",position:"relative",border:0,width:s?f(s):"100%"}).append(i(d,{class:o.sScrollHeadInner}).css({"box-sizing":"content-box",width:n.sXInner||"100%"}).append(c.removeAttr("id").css("margin-left",0).append("top"===l?a:null).append(e.children("thead"))))).append(i(d,{class:o.sScrollBody}).css({overflow:"auto",height:f(r),width:f(s)}).append(e));u&&p.append(i(d,{class:o.sScrollFoot}).css({overflow:"hidden",border:0,width:s?f(s):"100%"}).append(i(d,{class:o.sScrollFootInner}).append(h.removeAttr("id").css("margin-left",0).append("bottom"===l?a:null).append(e.children("tfoot")))));var g=p.children(),m=g[0],v=g[1],b=u?g[2]:null;return s&&i(v).scroll(function(t){var e=this.scrollLeft;m.scrollLeft=e,u&&(b.scrollLeft=e)}),t.nScrollHead=m,t.nScrollBody=v,t.nScrollFoot=b,t.aoDrawCallback.push({fn:Ut,sName:"scrolling"}),p[0]}function Ut(t){var e,n,s,r,o,a,l,c,h,u=t.oScroll,d=u.sX,f=u.sXInner,p=u.sY,g=u.iBarWidth,m=i(t.nScrollHead),v=m[0].style,b=m.children("div"),y=b[0].style,_=b.children("table"),w=t.nScrollBody,C=i(w),S=w.style,x=i(t.nScrollFoot).children("div"),T=x.children("table"),D=i(t.nTHead),E=i(t.nTable),I=E[0],A=I.style,k=t.nTFoot?i(t.nTFoot):null,O=t.oBrowser,N=O.bScrollOversize,P=[],L=[],R=[],F=function(t){var e=t.style;e.paddingTop="0",e.paddingBottom="0",e.borderTopWidth="0",e.borderBottomWidth="0",e.height=0};if(E.children("thead, tfoot").remove(),o=D.clone().prependTo(E),e=D.find("tr"),s=o.find("tr"),o.find("th, td").removeAttr("tabindex"),k&&(a=k.clone().prependTo(E),n=k.find("tr"),r=a.find("tr")),d||(S.width="100%",m[0].style.width="100%"),i.each(dt(t,o),function(e,n){l=M(t,e),n.style.width=t.aoColumns[l].sWidth}),k&&Vt(function(t){t.style.width=""},r),u.bCollapse&&""!==p&&(S.height=C[0].offsetHeight+D[0].offsetHeight+"px"),h=E.outerWidth(),""===d?(A.width="100%",N&&(E.find("tbody").height()>w.offsetHeight||"scroll"==C.css("overflow-y"))&&(A.width=Jt(E.outerWidth()-g))):""!==f?A.width=Jt(f):h==C.width()&&C.height()<E.height()?(A.width=Jt(h-g),E.outerWidth()>h-g&&(A.width=Jt(h))):A.width=Jt(h),h=E.outerWidth(),Vt(F,s),Vt(function(t){R.push(t.innerHTML),P.push(Jt(i(t).css("width")))},s),Vt(function(t,e){t.style.width=P[e]},e),i(s).height(0),k&&(Vt(F,r),Vt(function(t){L.push(Jt(i(t).css("width")))},r),Vt(function(t,e){t.style.width=L[e]},n),i(r).height(0)),Vt(function(t,e){t.innerHTML='<div class="dataTables_sizing" style="height:0;overflow:hidden;">'+R[e]+"</div>",t.style.width=P[e]},s),k&&Vt(function(t,e){t.innerHTML="",t.style.width=L[e]},r),E.outerWidth()<h?(c=w.scrollHeight>w.offsetHeight||"scroll"==C.css("overflow-y")?h+g:h,N&&(w.scrollHeight>w.offsetHeight||"scroll"==C.css("overflow-y"))&&(A.width=Jt(c-g)),""!==d&&""===f||ce(t,1,"Possible column misalignment",6)):c="100%",S.width=Jt(c),v.width=Jt(c),k&&(t.nScrollFoot.style.width=Jt(c)),p||N&&(S.height=Jt(I.offsetHeight+g)),p&&u.bCollapse){S.height=Jt(p);var j=d&&I.offsetWidth>w.offsetWidth?g:0;I.offsetHeight<w.offsetHeight&&(S.height=Jt(I.offsetHeight+j))}var H=E.outerWidth();_[0].style.width=Jt(H),y.width=Jt(H);var $=E.height()>w.clientHeight||"scroll"==C.css("overflow-y"),W="padding"+(O.bScrollbarLeft?"Left":"Right");y[W]=$?g+"px":"0px",k&&(T[0].style.width=Jt(H),x[0].style.width=Jt(H),x[0].style[W]=$?g+"px":"0px"),C.scroll(),!t.bSorted&&!t.bFiltered||t._drawHold||(w.scrollTop=0)}function Vt(t,e,n){for(var i,s,r=0,o=0,a=e.length;o<a;){for(i=e[o].firstChild,s=n?n[o].firstChild:null;i;)1===i.nodeType&&(n?t(i,s,r):t(i,r),r++),i=i.nextSibling,s=n?s.nextSibling:null;o++}}var Bt=/<.*?>/g;function zt(e){var n,s,r,o,a,l=e.nTable,c=e.aoColumns,h=e.oScroll,u=h.sY,d=h.sX,f=h.sXInner,p=c.length,g=W(e,"bVisible"),m=i("th",e.nTHead),v=l.getAttribute("width"),b=l.parentNode,y=!1;for(n=0;n<g.length;n++)null!==(s=c[g[n]]).sWidth&&(s.sWidth=qt(s.sWidthOrig,b),y=!0);if(y||d||u||p!=$(e)||p!=m.length){var _=i(l).clone().empty().css("visibility","hidden").removeAttr("id").append(i(e.nTHead).clone(!1)).append(i(e.nTFoot).clone(!1)).append(i("<tbody><tr/></tbody>"));_.find("tfoot th, tfoot td").css("width","");var w=_.find("tbody tr");for(m=dt(e,_.find("thead")[0]),n=0;n<g.length;n++)s=c[g[n]],m[n].style.width=null!==s.sWidthOrig&&""!==s.sWidthOrig?Jt(s.sWidthOrig):"";if(e.aoData.length)for(n=0;n<g.length;n++)s=c[r=g[n]],i(Qt(e,r)).clone(!1).append(s.sContentPadding).appendTo(w);if(_.appendTo(b),d&&f?_.width(f):d?(_.css("width","auto"),_.width()<b.offsetWidth&&_.width(b.offsetWidth)):u?_.width(b.offsetWidth):v&&_.width(v),Gt(e,_[0]),d){var C=0;for(n=0;n<g.length;n++)s=c[g[n]],a=i(m[n]).outerWidth(),C+=null===s.sWidthOrig?a:parseInt(s.sWidth,10)+a-i(m[n]).width();_.width(Jt(C)),l.style.width=Jt(C)}for(n=0;n<g.length;n++)s=c[g[n]],(o=i(m[n]).width())&&(s.sWidth=Jt(o));l.style.width=Jt(_.css("width")),_.remove()}else for(n=0;n<p;n++)c[n].sWidth=Jt(m.eq(n).width());v&&(l.style.width=Jt(v)),!v&&!d||e._reszEvt||(i(t).bind("resize.DT-"+e.sInstance,Kt(function(){j(e)})),e._reszEvt=!0)}function Kt(t,e){var i,s,r=e!==n?e:200;return function(){var e=this,o=+new Date,a=arguments;i&&o<i+r?(clearTimeout(s),s=setTimeout(function(){i=n,t.apply(e,a)},r)):i?(i=o,t.apply(e,a)):i=o}}function qt(t,n){if(!t)return 0;var s=i("<div/>").css("width",Jt(t)).appendTo(n||e.body),r=s[0].offsetWidth;return s.remove(),r}function Gt(t,e){var n=t.oScroll;if(n.sX||n.sY){var s=n.sX?0:n.iBarWidth;e.style.width=Jt(i(e).outerWidth()-s)}}function Qt(t,e){var n=Yt(t,e);if(n<0)return null;var s=t.aoData[n];return s.nTr?s.anCells[e]:i("<td/>").html(K(t,n,e,"display"))[0]}function Yt(t,e){for(var n,i=-1,s=-1,r=0,o=t.aoData.length;r<o;r++)(n=(n=K(t,r,e,"display")+"").replace(Bt,"")).length>i&&(i=n.length,s=r);return s}function Jt(t){return null===t?"0px":"number"==typeof t?t<0?"0px":t+"px":t.match(/\d$/)?t+"px":t}function Xt(){if(!s.__scrollbarWidth){var t=i("<p/>").css({width:"100%",height:200,padding:0})[0],e=i("<div/>").css({position:"absolute",top:0,left:0,width:200,height:150,padding:0,overflow:"hidden",visibility:"hidden"}).append(t).appendTo("body"),n=t.offsetWidth;e.css("overflow","scroll");var r=t.offsetWidth;n===r&&(r=e[0].clientWidth),e.remove(),s.__scrollbarWidth=n-r}return s.__scrollbarWidth}function Zt(t){var e,r,o,a,l,c,h,u=[],d=t.aoColumns,f=t.aaSortingFixed,p=i.isPlainObject(f),g=[],m=function(t){t.length&&!i.isArray(t[0])?g.push(t):g.push.apply(g,t)};for(i.isArray(f)&&m(f),p&&f.pre&&m(f.pre),m(t.aaSorting),p&&f.post&&m(f.post),e=0;e<g.length;e++)for(r=0,o=(a=d[h=g[e][0]].aDataSort).length;r<o;r++)c=d[l=a[r]].sType||"string",g[e]._idx===n&&(g[e]._idx=i.inArray(g[e][1],d[l].asSorting)),u.push({src:h,col:l,dir:g[e][1],index:g[e]._idx,type:c,formatter:s.ext.type.order[c+"-pre"]});return u}function te(t){var e,n,i,r,o,a=[],l=s.ext.type.order,c=t.aoData,h=(t.aoColumns,0),u=t.aiDisplayMaster;for(U(t),e=0,n=(o=Zt(t)).length;e<n;e++)(r=o[e]).formatter&&h++,re(t,r.col);if("ssp"!=ve(t)&&0!==o.length){for(e=0,i=u.length;e<i;e++)a[u[e]]=e;h===o.length?u.sort(function(t,e){var n,i,s,r,l,h=o.length,u=c[t]._aSortData,d=c[e]._aSortData;for(s=0;s<h;s++)if(0!==(r=(n=u[(l=o[s]).col])<(i=d[l.col])?-1:n>i?1:0))return"asc"===l.dir?r:-r;return(n=a[t])<(i=a[e])?-1:n>i?1:0}):u.sort(function(t,e){var n,i,s,r,h,u=o.length,d=c[t]._aSortData,f=c[e]._aSortData;for(s=0;s<u;s++)if(n=d[(h=o[s]).col],i=f[h.col],0!==(r=(l[h.type+"-"+h.dir]||l["string-"+h.dir])(n,i)))return r;return(n=a[t])<(i=a[e])?-1:n>i?1:0})}t.bSorted=!0}function ee(t){for(var e,n,i=t.aoColumns,s=Zt(t),r=t.oLanguage.oAria,o=0,a=i.length;o<a;o++){var l=i[o],c=l.asSorting,h=l.sTitle.replace(/<.*?>/g,""),u=l.nTh;u.removeAttribute("aria-sort"),l.bSortable?(s.length>0&&s[0].col==o?(u.setAttribute("aria-sort","asc"==s[0].dir?"ascending":"descending"),n=c[s[0].index+1]||c[0]):n=c[0],e=h+("asc"===n?r.sSortAscending:r.sSortDescending)):e=h,u.setAttribute("aria-label",e)}}function ne(t,e,s,r){var o,a=t.aoColumns[e],l=t.aaSorting,c=a.asSorting,h=function(t,e){var s=t._idx;return s===n&&(s=i.inArray(t[1],c)),s+1<c.length?s+1:e?null:0};if("number"==typeof l[0]&&(l=t.aaSorting=[l]),s&&t.oFeatures.bSortMulti){var u=i.inArray(e,w(l,"0"));-1!==u?null===(o=h(l[u],!0))?l.splice(u,1):(l[u][1]=c[o],l[u]._idx=o):(l.push([e,c[0],0]),l[l.length-1]._idx=0)}else l.length&&l[0][0]==e?(o=h(l[0]),l.length=1,l[0][1]=c[o],l[0]._idx=o):(l.length=0,l.push([e,c[0]]),l[0]._idx=0);ct(t),"function"==typeof r&&r(t)}function ie(t,e,n,i){var s=t.aoColumns[n];de(e,{},function(e){!1!==s.bSortable&&(t.oFeatures.bProcessing?($t(t,!0),setTimeout(function(){ne(t,n,e.shiftKey,i),"ssp"!==ve(t)&&$t(t,!1)},0)):ne(t,n,e.shiftKey,i))})}function se(t){var e,n,s,r=t.aLastSort,o=t.oClasses.sSortColumn,a=Zt(t),l=t.oFeatures;if(l.bSort&&l.bSortClasses){for(e=0,n=r.length;e<n;e++)s=r[e].src,i(w(t.aoData,"anCells",s)).removeClass(o+(e<2?e+1:3));for(e=0,n=a.length;e<n;e++)s=a[e].src,i(w(t.aoData,"anCells",s)).addClass(o+(e<2?e+1:3))}t.aLastSort=a}function re(t,e){var n,i,r,o=t.aoColumns[e],a=s.ext.order[o.sSortDataType];a&&(n=a.call(t.oInstance,t,e,H(t,e)));for(var l=s.ext.type.order[o.sType+"-pre"],c=0,h=t.aoData.length;c<h;c++)(i=t.aoData[c])._aSortData||(i._aSortData=[]),i._aSortData[e]&&!a||(r=a?n[c]:K(t,c,e,"sort"),i._aSortData[e]=l?l(r):r)}function oe(t){if(t.oFeatures.bStateSave&&!t.bDestroying){var e={time:+new Date,start:t._iDisplayStart,length:t._iDisplayLength,order:i.extend(!0,[],t.aaSorting),search:It(t.oPreviousSearch),columns:i.map(t.aoColumns,function(e,n){return{visible:e.bVisible,search:It(t.aoPreSearchCols[n])}})};pe(t,"aoStateSaveParams","stateSaveParams",[t,e]),t.oSavedState=e,t.fnStateSaveCallback.call(t.oInstance,t,e)}}function ae(t,e){var n,s,r=t.aoColumns;if(t.oFeatures.bStateSave){var o=t.fnStateLoadCallback.call(t.oInstance,t);if(o&&o.time){var a=pe(t,"aoStateLoadParams","stateLoadParams",[t,o]);if(-1===i.inArray(!1,a)){var l=t.iStateDuration;if(!(l>0&&o.time<+new Date-1e3*l)&&r.length===o.columns.length){for(t.oLoadedState=i.extend(!0,{},o),t._iDisplayStart=o.start,t.iInitDisplayStart=o.start,t._iDisplayLength=o.length,t.aaSorting=[],i.each(o.order,function(e,n){t.aaSorting.push(n[0]>=r.length?[0,n[1]]:n)}),i.extend(t.oPreviousSearch,At(o.search)),n=0,s=o.columns.length;n<s;n++){var c=o.columns[n];r[n].bVisible=c.visible,i.extend(t.aoPreSearchCols[n],At(c.search))}pe(t,"aoStateLoaded","stateLoaded",[t,o])}}}}}function le(t){var e=s.settings,n=i.inArray(t,w(e,"nTable"));return-1!==n?e[n]:null}function ce(e,n,i,r){if(i="DataTables warning: "+(null!==e?"table id="+e.sTableId+" - ":"")+i,r&&(i+=". For more information about this error, please see http://datatables.net/tn/"+r),n)t.console&&console.log&&console.log(i);else{var o=s.ext;if("alert"!=(o.sErrMode||o.errMode))throw new Error(i);alert(i)}}function he(t,e,s,r){i.isArray(s)?i.each(s,function(n,s){i.isArray(s)?he(t,e,s[0],s[1]):he(t,e,s)}):(r===n&&(r=s),e[s]!==n&&(t[r]=e[s]))}function ue(t,e,n){var s;for(var r in e)e.hasOwnProperty(r)&&(s=e[r],i.isPlainObject(s)?(i.isPlainObject(t[r])||(t[r]={}),i.extend(!0,t[r],s)):n&&"data"!==r&&"aaData"!==r&&i.isArray(s)?t[r]=s.slice():t[r]=s);return t}function de(t,e,n){i(t).bind("click.DT",e,function(e){t.blur(),n(e)}).bind("keypress.DT",e,function(t){13===t.which&&(t.preventDefault(),n(t))}).bind("selectstart.DT",function(){return!1})}function fe(t,e,n,i){n&&t[e].push({fn:n,sName:i})}function pe(t,e,n,s){var r=[];return e&&(r=i.map(t[e].slice().reverse(),function(e,n){return e.fn.apply(t.oInstance,s)})),null!==n&&i(t.nTable).trigger(n+".dt",s),r}function ge(t){var e=t._iDisplayStart,n=t.fnDisplayEnd(),i=t._iDisplayLength;e>=n&&(e=n-i),e-=e%i,(-1===i||e<0)&&(e=0),t._iDisplayStart=e}function me(t,e){var n=t.renderer,r=s.ext.renderer[e];return i.isPlainObject(n)&&n[e]?r[n[e]]||r._:"string"==typeof n&&r[n]||r._}function ve(t){return t.oFeatures.bServerSide?"ssp":t.ajax||t.sAjaxSource?"ajax":"dom"}s=function(t){this.$=function(t,e){return this.api(!0).$(t,e)},this._=function(t,e){return this.api(!0).rows(t,e).data()},this.api=function(t){return new o(t?le(this[r.iApiIndex]):this)},this.fnAddData=function(t,e){var s=this.api(!0),r=i.isArray(t)&&(i.isArray(t[0])||i.isPlainObject(t[0]))?s.rows.add(t):s.row.add(t);return(e===n||e)&&s.draw(),r.flatten().toArray()},this.fnAdjustColumnSizing=function(t){var e=this.api(!0).columns.adjust(),i=e.settings()[0],s=i.oScroll;t===n||t?e.draw(!1):""===s.sX&&""===s.sY||Ut(i)},this.fnClearTable=function(t){var e=this.api(!0).clear();(t===n||t)&&e.draw()},this.fnClose=function(t){this.api(!0).row(t).child.hide()},this.fnDeleteRow=function(t,e,i){var s=this.api(!0),r=s.rows(t),o=r.settings()[0],a=o.aoData[r[0][0]];return r.remove(),e&&e.call(this,o,a),(i===n||i)&&s.draw(),a},this.fnDestroy=function(t){this.api(!0).destroy(t)},this.fnDraw=function(t){this.api(!0).draw(!t)},this.fnFilter=function(t,e,i,s,r,o){var a=this.api(!0);null===e||e===n?a.search(t,i,s,o):a.column(e).search(t,i,s,o),a.draw()},this.fnGetData=function(t,e){var i=this.api(!0);if(t!==n){var s=t.nodeName?t.nodeName.toLowerCase():"";return e!==n||"td"==s||"th"==s?i.cell(t,e).data():i.row(t).data()||null}return i.data().toArray()},this.fnGetNodes=function(t){var e=this.api(!0);return t!==n?e.row(t).node():e.rows().nodes().flatten().toArray()},this.fnGetPosition=function(t){var e=this.api(!0),n=t.nodeName.toUpperCase();if("TR"==n)return e.row(t).index();if("TD"==n||"TH"==n){var i=e.cell(t).index();return[i.row,i.columnVisible,i.column]}return null},this.fnIsOpen=function(t){return this.api(!0).row(t).child.isShown()},this.fnOpen=function(t,e,n){return this.api(!0).row(t).child(e,n).show().child()[0]},this.fnPageChange=function(t,e){var i=this.api(!0).page(t);(e===n||e)&&i.draw(!1)},this.fnSetColumnVis=function(t,e,i){var s=this.api(!0).column(t).visible(e);(i===n||i)&&s.columns.adjust().draw()},this.fnSettings=function(){return le(this[r.iApiIndex])},this.fnSort=function(t){this.api(!0).order(t).draw()},this.fnSortListener=function(t,e,n){this.api(!0).order.listener(t,e,n)},this.fnUpdate=function(t,e,i,s,r){var o=this.api(!0);return i===n||null===i?o.row(e).data(t):o.cell(e,i).data(t),(r===n||r)&&o.columns.adjust(),(s===n||s)&&o.draw(),0},this.fnVersionCheck=r.fnVersionCheck;var e=this,a=t===n,l=this.length;for(var c in a&&(t={}),this.oApi=this.internal=r.internal,s.ext.internal)c&&(this[c]=Le(c));return this.each(function(){var r,o=l>1?ue({},t,!0):t,c=0,h=this.getAttribute("id"),u=!1,d=s.defaults;if("table"==this.nodeName.toLowerCase()){O(d),N(d.column),I(d,d,!0),I(d.column,d.column,!0),I(d,o);var f=s.settings;for(c=0,r=f.length;c<r;c++){if(f[c].nTable==this){var p=o.bRetrieve!==n?o.bRetrieve:d.bRetrieve,g=o.bDestroy!==n?o.bDestroy:d.bDestroy;if(a||p)return f[c].oInstance;if(g){f[c].oInstance.fnDestroy();break}return void ce(f[c],0,"Cannot reinitialise DataTable",3)}if(f[c].sTableId==this.id){f.splice(c,1);break}}null!==h&&""!==h||(h="DataTables_Table_"+s.ext._unique++,this.id=h);var m=i.extend(!0,{},s.models.oSettings,{nTable:this,oApi:e.internal,oInit:o,sDestroyWidth:i(this)[0].style.width,sInstance:h,sTableId:h});f.push(m),m.oInstance=1===e.length?e:i(this).dataTable(),O(o),o.oLanguage&&A(o.oLanguage),o.aLengthMenu&&!o.iDisplayLength&&(o.iDisplayLength=i.isArray(o.aLengthMenu[0])?o.aLengthMenu[0][0]:o.aLengthMenu[0]),o=ue(i.extend(!0,{},d),o),he(m.oFeatures,o,["bPaginate","bLengthChange","bFilter","bSort","bSortMulti","bInfo","bProcessing","bAutoWidth","bSortClasses","bServerSide","bDeferRender"]),he(m,o,["asStripeClasses","ajax","fnServerData","fnFormatNumber","sServerMethod","aaSorting","aaSortingFixed","aLengthMenu","sPaginationType","sAjaxSource","sAjaxDataProp","iStateDuration","sDom","bSortCellsTop","iTabIndex","fnStateLoadCallback","fnStateSaveCallback","renderer","searchDelay",["iCookieDuration","iStateDuration"],["oSearch","oPreviousSearch"],["aoSearchCols","aoPreSearchCols"],["iDisplayLength","_iDisplayLength"],["bJQueryUI","bJUI"]]),he(m.oScroll,o,[["sScrollX","sX"],["sScrollXInner","sXInner"],["sScrollY","sY"],["bScrollCollapse","bCollapse"]]),he(m.oLanguage,o,"fnInfoCallback"),fe(m,"aoDrawCallback",o.fnDrawCallback,"user"),fe(m,"aoServerParams",o.fnServerParams,"user"),fe(m,"aoStateSaveParams",o.fnStateSaveParams,"user"),fe(m,"aoStateLoadParams",o.fnStateLoadParams,"user"),fe(m,"aoStateLoaded",o.fnStateLoaded,"user"),fe(m,"aoRowCallback",o.fnRowCallback,"user"),fe(m,"aoRowCreatedCallback",o.fnCreatedRow,"user"),fe(m,"aoHeaderCallback",o.fnHeaderCallback,"user"),fe(m,"aoFooterCallback",o.fnFooterCallback,"user"),fe(m,"aoInitComplete",o.fnInitComplete,"user"),fe(m,"aoPreDrawCallback",o.fnPreDrawCallback,"user");var v=m.oClasses;if(o.bJQueryUI?(i.extend(v,s.ext.oJUIClasses,o.oClasses),o.sDom===d.sDom&&"lfrtip"===d.sDom&&(m.sDom='<"H"lfr>t<"F"ip>'),m.renderer?i.isPlainObject(m.renderer)&&!m.renderer.header&&(m.renderer.header="jqueryui"):m.renderer="jqueryui"):i.extend(v,s.ext.classes,o.oClasses),i(this).addClass(v.sTable),""===m.oScroll.sX&&""===m.oScroll.sY||(m.oScroll.iBarWidth=Xt()),!0===m.oScroll.sX&&(m.oScroll.sX="100%"),m.iInitDisplayStart===n&&(m.iInitDisplayStart=o.iDisplayStart,m._iDisplayStart=o.iDisplayStart),null!==o.iDeferLoading){m.bDeferLoading=!0;var b=i.isArray(o.iDeferLoading);m._iRecordsDisplay=b?o.iDeferLoading[0]:o.iDeferLoading,m._iRecordsTotal=b?o.iDeferLoading[1]:o.iDeferLoading}var y=m.oLanguage;i.extend(!0,y,o.oLanguage),""!==y.sUrl&&(i.ajax({dataType:"json",url:y.sUrl,success:function(t){A(t),I(d.oLanguage,t),i.extend(!0,y,t),Pt(m)},error:function(){Pt(m)}}),u=!0),null===o.asStripeClasses&&(m.asStripeClasses=[v.sStripeOdd,v.sStripeEven]);var _=m.asStripeClasses,w=i("tbody tr:eq(0)",this);-1!==i.inArray(!0,i.map(_,function(t,e){return w.hasClass(t)}))&&(i("tbody tr",this).removeClass(_.join(" ")),m.asDestroyStripes=_.slice());var C,S=[],x=this.getElementsByTagName("thead");if(0!==x.length&&(ut(m.aoHeader,x[0]),S=dt(m)),null===o.aoColumns)for(C=[],c=0,r=S.length;c<r;c++)C.push(null);else C=o.aoColumns;for(c=0,r=C.length;c<r;c++)R(m,S?S[c]:null);if(V(m,o.aoColumnDefs,C,function(t,e){F(m,t,e)}),w.length){var T=function(t,e){return t.getAttribute("data-"+e)?e:null};i.each(it(m,w[0]).cells,function(t,e){var i=m.aoColumns[t];if(i.mData===t){var s=T(e,"sort")||T(e,"order"),r=T(e,"filter")||T(e,"search");null===s&&null===r||(i.mData={_:t+".display",sort:null!==s?t+".@data-"+s:n,type:null!==s?t+".@data-"+s:n,filter:null!==r?t+".@data-"+r:n},F(m,t))}})}var D=m.oFeatures;if(o.bStateSave&&(D.bStateSave=!0,ae(m),fe(m,"aoDrawCallback",oe,"state_save")),o.aaSorting===n){var E=m.aaSorting;for(c=0,r=E.length;c<r;c++)E[c][1]=m.aoColumns[c].asSorting[0]}se(m),D.bSort&&fe(m,"aoDrawCallback",function(){if(m.bSorted){var t=Zt(m),e={};i.each(t,function(t,n){e[n.src]=n.dir}),pe(m,null,"order",[m,t,e]),ee(m)}}),fe(m,"aoDrawCallback",function(){(m.bSorted||"ssp"===ve(m)||D.bDeferRender)&&se(m)},"sc"),P(m);var k=i(this).children("caption").each(function(){this._captionSide=i(this).css("caption-side")}),L=i(this).children("thead");0===L.length&&(L=i("<thead/>").appendTo(this)),m.nTHead=L[0];var j=i(this).children("tbody");0===j.length&&(j=i("<tbody/>").appendTo(this)),m.nTBody=j[0];var M=i(this).children("tfoot");if(0===M.length&&k.length>0&&(""!==m.oScroll.sX||""!==m.oScroll.sY)&&(M=i("<tfoot/>").appendTo(this)),0===M.length||0===M.children().length?i(this).addClass(v.sNoFooter):M.length>0&&(m.nTFoot=M[0],ut(m.aoFooter,m.nTFoot)),o.aaData)for(c=0;c<o.aaData.length;c++)B(m,o.aaData[c]);else(m.bDeferLoading||"dom"==ve(m))&&z(m,i(m.nTBody).children("tr"));m.aiDisplay=m.aiDisplayMaster.slice(),m.bInitialised=!0,!1===u&&Pt(m)}else ce(null,0,"Non-table node initialisation ("+this.nodeName+")",2)}),e=null,this};var be=[],ye=Array.prototype;o=function(t,e){if(!this instanceof o)throw"DT API must be constructed as a new object";var n=[],r=function(t){var e=function(t){var e,n,r=s.settings,o=i.map(r,function(t,e){return t.nTable});return t?t.nTable&&t.oApi?[t]:t.nodeName&&"table"===t.nodeName.toLowerCase()?-1!==(e=i.inArray(t,o))?[r[e]]:null:t&&"function"==typeof t.settings?t.settings().toArray():("string"==typeof t?n=i(t):t instanceof i&&(n=t),n?n.map(function(t){return-1!==(e=i.inArray(this,o))?r[e]:null}).toArray():void 0):[]}(t);e&&n.push.apply(n,e)};if(i.isArray(t))for(var a=0,l=t.length;a<l;a++)r(t[a]);else r(t);this.context=D(n),e&&this.push.apply(this,e.toArray?e.toArray():e),this.selector={rows:null,cols:null,opts:null},o.extend(this,this,be)},s.Api=o,o.prototype={concat:ye.concat,context:[],each:function(t){for(var e=0,n=this.length;e<n;e++)t.call(this,this[e],e,this);return this},eq:function(t){var e=this.context;return e.length>t?new o(e[t],this[t]):null},filter:function(t){var e=[];if(ye.filter)e=ye.filter.call(this,t,this);else for(var n=0,i=this.length;n<i;n++)t.call(this,this[n],n,this)&&e.push(this[n]);return new o(this.context,e)},flatten:function(){var t=[];return new o(this.context,t.concat.apply(t,this.toArray()))},join:ye.join,indexOf:ye.indexOf||function(t,e){for(var n=e||0,i=this.length;n<i;n++)if(this[n]===t)return n;return-1},iterator:function(t,e,i,s){var r,a,l,c,h,u,d,f,p=[],g=this.context,m=this.selector;for("string"==typeof t&&(s=i,i=e,e=t,t=!1),a=0,l=g.length;a<l;a++){var v=new o(g[a]);if("table"===e)(r=i.call(v,g[a],a))!==n&&p.push(r);else if("columns"===e||"rows"===e)(r=i.call(v,g[a],this[a],a))!==n&&p.push(r);else if("column"===e||"column-rows"===e||"row"===e||"cell"===e)for(d=this[a],"column-rows"===e&&(u=xe(g[a],m.opts)),c=0,h=d.length;c<h;c++)f=d[c],(r="cell"===e?i.call(v,g[a],f.row,f.column,a,c):i.call(v,g[a],f,a,c,u))!==n&&p.push(r)}if(p.length||s){var b=new o(g,t?p.concat.apply([],p):p),y=b.selector;return y.rows=m.rows,y.cols=m.cols,y.opts=m.opts,b}return this},lastIndexOf:ye.lastIndexOf||function(t,e){return this.indexOf.apply(this.toArray.reverse(),arguments)},length:0,map:function(t){var e=[];if(ye.map)e=ye.map.call(this,t,this);else for(var n=0,i=this.length;n<i;n++)e.push(t.call(this,this[n],n));return new o(this.context,e)},pluck:function(t){return this.map(function(e){return e[t]})},pop:ye.pop,push:ye.push,reduce:ye.reduce||function(t,e){return L(this,t,e,0,this.length,1)},reduceRight:ye.reduceRight||function(t,e){return L(this,t,e,this.length-1,-1,-1)},reverse:ye.reverse,selector:null,shift:ye.shift,sort:ye.sort,splice:ye.splice,toArray:function(){return ye.slice.call(this)},to$:function(){return i(this)},toJQuery:function(){return i(this)},unique:function(){return new o(this.context,D(this))},unshift:ye.unshift},o.extend=function(t,e,n){if(e&&(e instanceof o||e.__dt_wrapper)){var s,r,a,l=function(t,e,n){return function(){var i=e.apply(t,arguments);return o.extend(i,i,n.methodExt),i}};for(s=0,r=n.length;s<r;s++)e[(a=n[s]).name]="function"==typeof a.val?l(t,a.val,a):i.isPlainObject(a.val)?{}:a.val,e[a.name].__dt_wrapper=!0,o.extend(t,e[a.name],a.propExt)}},o.register=a=function(t,e){if(i.isArray(t))for(var n=0,s=t.length;n<s;n++)o.register(t[n],e);else{var r,a,l,c,h=t.split("."),u=be,d=function(t,e){for(var n=0,i=t.length;n<i;n++)if(t[n].name===e)return t[n];return null};for(r=0,a=h.length;r<a;r++){var f=d(u,l=(c=-1!==h[r].indexOf("()"))?h[r].replace("()",""):h[r]);f||(f={name:l,val:{},methodExt:[],propExt:[]},u.push(f)),r===a-1?f.val=e:u=c?f.methodExt:f.propExt}}},o.registerPlural=l=function(t,e,s){o.register(t,s),o.register(e,function(){var t=s.apply(this,arguments);return t===this?this:t instanceof o?t.length?i.isArray(t[0])?new o(t.context,t[0]):t[0]:n:t})};a("tables()",function(t){return t?new o(function(t,e){if("number"==typeof t)return[e[t]];var n=i.map(e,function(t,e){return t.nTable});return i(n).filter(t).map(function(t){var s=i.inArray(this,n);return e[s]}).toArray()}(t,this.context)):this}),a("table()",function(t){var e=this.tables(t),n=e.context;return n.length?new o(n[0]):e}),l("tables().nodes()","table().node()",function(){return this.iterator("table",function(t){return t.nTable},1)}),l("tables().body()","table().body()",function(){return this.iterator("table",function(t){return t.nTBody},1)}),l("tables().header()","table().header()",function(){return this.iterator("table",function(t){return t.nTHead},1)}),l("tables().footer()","table().footer()",function(){return this.iterator("table",function(t){return t.nTFoot},1)}),l("tables().containers()","table().container()",function(){return this.iterator("table",function(t){return t.nTableWrapper},1)}),a("draw()",function(t){return this.iterator("table",function(e){ct(e,!1===t)})}),a("page()",function(t){return t===n?this.page.info().page:this.iterator("table",function(e){Mt(e,t)})}),a("page.info()",function(t){if(0===this.context.length)return n;var e=this.context[0],i=e._iDisplayStart,s=e._iDisplayLength,r=e.fnRecordsDisplay(),o=-1===s;return{page:o?0:Math.floor(i/s),pages:o?1:Math.ceil(r/s),start:i,end:e.fnDisplayEnd(),length:s,recordsTotal:e.fnRecordsTotal(),recordsDisplay:r}}),a("page.len()",function(t){return t===n?0!==this.context.length?this.context[0]._iDisplayLength:n:this.iterator("table",function(e){Rt(e,t)})});var _e=function(t,e,n){if("ssp"==ve(t)?ct(t,e):($t(t,!0),ft(t,[],function(n){tt(t);for(var i=vt(t,n),s=0,r=i.length;s<r;s++)B(t,i[s]);ct(t,e),$t(t,!1)})),n){var i=new o(t);i.one("draw",function(){n(i.ajax.json())})}};a("ajax.json()",function(){var t=this.context;if(t.length>0)return t[0].json}),a("ajax.params()",function(){var t=this.context;if(t.length>0)return t[0].oAjaxData}),a("ajax.reload()",function(t,e){return this.iterator("table",function(n){_e(n,!1===e,t)})}),a("ajax.url()",function(t){var e=this.context;return t===n?0===e.length?n:(e=e[0]).ajax?i.isPlainObject(e.ajax)?e.ajax.url:e.ajax:e.sAjaxSource:this.iterator("table",function(e){i.isPlainObject(e.ajax)?e.ajax.url=t:e.ajax=t})}),a("ajax.url().load()",function(t,e){return this.iterator("table",function(n){_e(n,!1===e,t)})});var we=function(t,e){var s,r,o,a,l,c,h=[],u=typeof t;for(t&&"string"!==u&&"function"!==u&&t.length!==n||(t=[t]),o=0,a=t.length;o<a;o++)for(l=0,c=(r=t[o]&&t[o].split?t[o].split(","):[t[o]]).length;l<c;l++)(s=e("string"==typeof r[l]?i.trim(r[l]):r[l]))&&s.length&&h.push.apply(h,s);return h},Ce=function(t){return t||(t={}),t.filter&&!t.search&&(t.search=t.filter),{search:t.search||"none",order:t.order||"current",page:t.page||"all"}},Se=function(t){for(var e=0,n=t.length;e<n;e++)if(t[e].length>0)return t[0]=t[e],t.length=1,t.context=[t.context[e]],t;return t.length=0,t},xe=function(t,e){var n,s,r,o=[],a=t.aiDisplay,l=t.aiDisplayMaster,c=e.search,h=e.order,u=e.page;if("ssp"==ve(t))return"removed"===c?[]:S(0,l.length);if("current"==u)for(n=t._iDisplayStart,s=t.fnDisplayEnd();n<s;n++)o.push(a[n]);else if("current"==h||"applied"==h)o="none"==c?l.slice():"applied"==c?a.slice():i.map(l,function(t,e){return-1===i.inArray(t,a)?t:null});else if("index"==h||"original"==h)for(n=0,s=t.aoData.length;n<s;n++)"none"==c?o.push(n):(-1===(r=i.inArray(n,a))&&"removed"==c||r>=0&&"applied"==c)&&o.push(n);return o};a("rows()",function(t,e){t===n?t="":i.isPlainObject(t)&&(e=t,t=""),e=Ce(e);var s=this.iterator("table",function(n){return function(t,e,n){return we(e,function(e){var s=v(e);if(null!==s&&!n)return[s];var r=xe(t,n);if(null!==s&&-1!==i.inArray(s,r))return[s];if(!e)return r;if("function"==typeof e)return i.map(r,function(n){var i=t.aoData[n];return e(n,i._aData,i.nTr)?n:null});var o=x(C(t.aoData,r,"nTr"));return e.nodeName&&-1!==i.inArray(e,o)?[e._DT_RowIndex]:i(o).filter(e).map(function(){return this._DT_RowIndex}).toArray()})}(n,t,e)},1);return s.selector.rows=t,s.selector.opts=e,s}),a("rows().nodes()",function(){return this.iterator("row",function(t,e){return t.aoData[e].nTr||n},1)}),a("rows().data()",function(){return this.iterator(!0,"rows",function(t,e){return C(t.aoData,e,"_aData")},1)}),l("rows().cache()","row().cache()",function(t){return this.iterator("row",function(e,n){var i=e.aoData[n];return"search"===t?i._aFilterData:i._aSortData},1)}),l("rows().invalidate()","row().invalidate()",function(t){return this.iterator("row",function(e,n){nt(e,n,t)})}),l("rows().indexes()","row().index()",function(){return this.iterator("row",function(t,e){return e},1)}),l("rows().remove()","row().remove()",function(){var t=this;return this.iterator("row",function(e,n,s){var r=e.aoData;r.splice(n,1);for(var o=0,a=r.length;o<a;o++)null!==r[o].nTr&&(r[o].nTr._DT_RowIndex=o);i.inArray(n,e.aiDisplay);et(e.aiDisplayMaster,n),et(e.aiDisplay,n),et(t[s],n,!1),ge(e)})}),a("rows.add()",function(t){var e=this.iterator("table",function(e){var n,i,s,r=[];for(i=0,s=t.length;i<s;i++)(n=t[i]).nodeName&&"TR"===n.nodeName.toUpperCase()?r.push(z(e,n)[0]):r.push(B(e,n));return r},1),n=this.rows(-1);return n.pop(),n.push.apply(n,e.toArray()),n}),a("row()",function(t,e){return Se(this.rows(t,e))}),a("row().data()",function(t){var e=this.context;return t===n?e.length&&this.length?e[0].aoData[this[0]]._aData:n:(e[0].aoData[this[0]]._aData=t,nt(e[0],this[0],"data"),this)}),a("row().node()",function(){var t=this.context;return t.length&&this.length&&t[0].aoData[this[0]].nTr||null}),a("row.add()",function(t){t instanceof i&&t.length&&(t=t[0]);var e=this.iterator("table",function(e){return t.nodeName&&"TR"===t.nodeName.toUpperCase()?z(e,t)[0]:B(e,t)});return this.row(e[0])});var Te=function(t,e){var i=t.context;if(i.length){var s=i[0].aoData[e!==n?e:t[0]];s._details&&(s._details.remove(),s._detailsShow=n,s._details=n)}},De=function(t,e){var n=t.context;if(n.length&&t.length){var i=n[0].aoData[t[0]];i._details&&(i._detailsShow=e,e?i._details.insertAfter(i.nTr):i._details.detach(),Ee(n[0]))}},Ee=function(t){var e=new o(t),n=t.aoData;e.off("draw.dt.DT_details column-visibility.dt.DT_details destroy.dt.DT_details"),w(n,"_details").length>0&&(e.on("draw.dt.DT_details",function(i,s){t===s&&e.rows({page:"current"}).eq(0).each(function(t){var e=n[t];e._detailsShow&&e._details.insertAfter(e.nTr)})}),e.on("column-visibility.dt.DT_details",function(e,i,s,r){if(t===i)for(var o,a=$(i),l=0,c=n.length;l<c;l++)(o=n[l])._details&&o._details.children("td[colspan]").attr("colspan",a)}),e.on("destroy.dt.DT_details",function(i,s){if(t===s)for(var r=0,o=n.length;r<o;r++)n[r]._details&&Te(e,r)}))};a("row().child()",function(t,e){var s=this.context;return t===n?s.length&&this.length?s[0].aoData[this[0]]._details:n:(!0===t?this.child.show():!1===t?Te(this):s.length&&this.length&&function(t,e,n,s){var r=[],o=function(e,n){if(e.nodeName&&"tr"===e.nodeName.toLowerCase())r.push(e);else{var s=i("<tr><td/></tr>").addClass(n);i("td",s).addClass(n).html(e)[0].colSpan=$(t),r.push(s[0])}};if(i.isArray(n)||n instanceof i)for(var a=0,l=n.length;a<l;a++)o(n[a],s);else o(n,s);e._details&&e._details.remove(),e._details=i(r),e._detailsShow&&e._details.insertAfter(e.nTr)}(s[0],s[0].aoData[this[0]],t,e),this)}),a(["row().child.show()","row().child().show()"],function(t){return De(this,!0),this}),a(["row().child.hide()","row().child().hide()"],function(){return De(this,!1),this}),a(["row().child.remove()","row().child().remove()"],function(){return Te(this),this}),a("row().child.isShown()",function(){var t=this.context;return t.length&&this.length&&t[0].aoData[this[0]]._detailsShow||!1});var Ie=/^(.+):(name|visIdx|visible)$/,Ae=function(t,e,n,i,s){for(var r=[],o=0,a=s.length;o<a;o++)r.push(K(t,s[o],e));return r};a("columns()",function(t,e){t===n?t="":i.isPlainObject(t)&&(e=t,t=""),e=Ce(e);var s=this.iterator("table",function(n){return function(t,e,n){var s=t.aoColumns,r=w(s,"sName"),o=w(s,"nTh");return we(e,function(e){var a=v(e);if(""===e)return S(s.length);if(null!==a)return[a>=0?a:s.length+a];if("function"==typeof e){var l=xe(t,n);return i.map(s,function(n,i){return e(i,Ae(t,i,0,0,l),o[i])?i:null})}var c="string"==typeof e?e.match(Ie):"";if(!c)return i(o).filter(e).map(function(){return i.inArray(this,o)}).toArray();switch(c[2]){case"visIdx":case"visible":var h=parseInt(c[1],10);if(h<0){var u=i.map(s,function(t,e){return t.bVisible?e:null});return[u[u.length+h]]}return[M(t,h)];case"name":return i.map(r,function(t,e){return t===c[1]?e:null})}})}(n,t,e)},1);return s.selector.cols=t,s.selector.opts=e,s}),l("columns().header()","column().header()",function(t,e){return this.iterator("column",function(t,e){return t.aoColumns[e].nTh},1)}),l("columns().footer()","column().footer()",function(t,e){return this.iterator("column",function(t,e){return t.aoColumns[e].nTf},1)}),l("columns().data()","column().data()",function(){return this.iterator("column-rows",Ae,1)}),l("columns().dataSrc()","column().dataSrc()",function(){return this.iterator("column",function(t,e){return t.aoColumns[e].mData},1)}),l("columns().cache()","column().cache()",function(t){return this.iterator("column-rows",function(e,n,i,s,r){return C(e.aoData,r,"search"===t?"_aFilterData":"_aSortData",n)},1)}),l("columns().nodes()","column().nodes()",function(){return this.iterator("column-rows",function(t,e,n,i,s){return C(t.aoData,s,"anCells",e)},1)}),l("columns().visible()","column().visible()",function(t,e){return this.iterator("column",function(s,r){if(t===n)return s.aoColumns[r].bVisible;!function(t,e,s,r){var o,a,l,c,h=t.aoColumns,u=h[e],d=t.aoData;if(s===n)return u.bVisible;if(u.bVisible!==s){if(s){var f=i.inArray(!0,w(h,"bVisible"),e+1);for(a=0,l=d.length;a<l;a++)c=d[a].nTr,o=d[a].anCells,c&&c.insertBefore(o[e],o[f]||null)}else i(w(t.aoData,"anCells",e)).detach();u.bVisible=s,at(t,t.aoHeader),at(t,t.aoFooter),(r===n||r)&&(j(t),(t.oScroll.sX||t.oScroll.sY)&&Ut(t)),pe(t,null,"column-visibility",[t,e,s]),oe(t)}}(s,r,t,e)})}),l("columns().indexes()","column().index()",function(t){return this.iterator("column",function(e,n){return"visible"===t?H(e,n):n},1)}),a("columns.adjust()",function(){return this.iterator("table",function(t){j(t)},1)}),a("column.index()",function(t,e){if(0!==this.context.length){var n=this.context[0];if("fromVisible"===t||"toData"===t)return M(n,e);if("fromData"===t||"toVisible"===t)return H(n,e)}}),a("column()",function(t,e){return Se(this.columns(t,e))});a("cells()",function(t,e,s){if(i.isPlainObject(t)&&(typeof t.row!==n?(s=e,e=null):(s=t,t=null)),i.isPlainObject(e)&&(s=e,e=null),null===e||e===n)return this.iterator("table",function(e){return function(t,e,s){var r,o,a,l,c,h,u,d=t.aoData,f=xe(t,s),p=x(C(d,f,"anCells")),g=i([].concat.apply([],p)),m=t.aoColumns.length;return we(e,function(e){var s="function"==typeof e;if(null===e||e===n||s){for(o=[],a=0,l=f.length;a<l;a++)for(r=f[a],c=0;c<m;c++)h={row:r,column:c},s?(u=t.aoData[r],e(h,K(t,r,c),u.anCells[c])&&o.push(h)):o.push(h);return o}return i.isPlainObject(e)?[e]:g.filter(e).map(function(t,e){return{row:r=e.parentNode._DT_RowIndex,column:i.inArray(e,d[r].anCells)}}).toArray()})}(e,t,Ce(s))});var r,o,a,l,c,h=this.columns(e,s),u=this.rows(t,s),d=this.iterator("table",function(t,e){for(r=[],o=0,a=u[e].length;o<a;o++)for(l=0,c=h[e].length;l<c;l++)r.push({row:u[e][o],column:h[e][l]});return r},1);return i.extend(d.selector,{cols:e,rows:t,opts:s}),d}),l("cells().nodes()","cell().node()",function(){return this.iterator("cell",function(t,e,i){var s=t.aoData[e].anCells;return s?s[i]:n},1)}),a("cells().data()",function(){return this.iterator("cell",function(t,e,n){return K(t,e,n)},1)}),l("cells().cache()","cell().cache()",function(t){return t="search"===t?"_aFilterData":"_aSortData",this.iterator("cell",function(e,n,i){return e.aoData[n][t][i]},1)}),l("cells().render()","cell().render()",function(t){return this.iterator("cell",function(e,n,i){return K(e,n,i,t)},1)}),l("cells().indexes()","cell().index()",function(){return this.iterator("cell",function(t,e,n){return{row:e,column:n,columnVisible:H(t,n)}},1)}),l("cells().invalidate()","cell().invalidate()",function(t){return this.iterator("cell",function(e,n,i){nt(e,n,t,i)})}),a("cell()",function(t,e,n){return Se(this.cells(t,e,n))}),a("cell().data()",function(t){var e=this.context,i=this[0];return t===n?e.length&&i.length?K(e[0],i[0].row,i[0].column):n:(q(e[0],i[0].row,i[0].column,t),nt(e[0],i[0].row,"data",i[0].column),this)}),a("order()",function(t,e){var s=this.context;return t===n?0!==s.length?s[0].aaSorting:n:("number"==typeof t?t=[[t,e]]:i.isArray(t[0])||(t=Array.prototype.slice.call(arguments)),this.iterator("table",function(e){e.aaSorting=t.slice()}))}),a("order.listener()",function(t,e,n){return this.iterator("table",function(i){ie(i,t,e,n)})}),a(["columns().order()","column().order()"],function(t){var e=this;return this.iterator("table",function(n,s){var r=[];i.each(e[s],function(e,n){r.push([n,t])}),n.aaSorting=r})}),a("search()",function(t,e,s,r){var o=this.context;return t===n?0!==o.length?o[0].oPreviousSearch.sSearch:n:this.iterator("table",function(n){n.oFeatures.bFilter&&yt(n,i.extend({},n.oPreviousSearch,{sSearch:t+"",bRegex:null!==e&&e,bSmart:null===s||s,bCaseInsensitive:null===r||r}),1)})}),l("columns().search()","column().search()",function(t,e,s,r){return this.iterator("column",function(o,a){var l=o.aoPreSearchCols;if(t===n)return l[a].sSearch;o.oFeatures.bFilter&&(i.extend(l[a],{sSearch:t+"",bRegex:null!==e&&e,bSmart:null===s||s,bCaseInsensitive:null===r||r}),yt(o,o.oPreviousSearch,1))})}),a("state()",function(){return this.context.length?this.context[0].oSavedState:null}),a("state.clear()",function(){return this.iterator("table",function(t){t.fnStateSaveCallback.call(t.oInstance,t,{})})}),a("state.loaded()",function(){return this.context.length?this.context[0].oLoadedState:null}),a("state.save()",function(){return this.iterator("table",function(t){oe(t)})}),s.versionCheck=s.fnVersionCheck=function(t){for(var e,n,i=s.version.split("."),r=t.split("."),o=0,a=r.length;o<a;o++)if((e=parseInt(i[o],10)||0)!==(n=parseInt(r[o],10)||0))return e>n;return!0},s.isDataTable=s.fnIsDataTable=function(t){var e=i(t).get(0),n=!1;return i.each(s.settings,function(t,i){i.nTable!==e&&i.nScrollHead!==e&&i.nScrollFoot!==e||(n=!0)}),n},s.tables=s.fnTables=function(t){return i.map(s.settings,function(e){if(!t||t&&i(e.nTable).is(":visible"))return e.nTable})},s.util={throttle:Kt,escapeRegex:xt},s.camelToHungarian=I,a("$()",function(t,e){var n=this.rows(e).nodes(),s=i(n);return i([].concat(s.filter(t).toArray(),s.find(t).toArray()))}),i.each(["on","one","off"],function(t,e){a(e+"()",function(){var t=Array.prototype.slice.call(arguments);t[0].match(/\.dt\b/)||(t[0]+=".dt");var n=i(this.tables().nodes());return n[e].apply(n,t),this})}),a("clear()",function(){return this.iterator("table",function(t){tt(t)})}),a("settings()",function(){return new o(this.context,this.context)}),a("data()",function(){return this.iterator("table",function(t){return w(t.aoData,"_aData")}).flatten()}),a("destroy()",function(e){return e=e||!1,this.iterator("table",function(n){var r,a=n.nTableWrapper.parentNode,l=n.oClasses,c=n.nTable,h=n.nTBody,u=n.nTHead,d=n.nTFoot,f=i(c),p=i(h),g=i(n.nTableWrapper),m=i.map(n.aoData,function(t){return t.nTr});n.bDestroying=!0,pe(n,"aoDestroyCallback","destroy",[n]),e||new o(n).columns().visible(!0),g.unbind(".DT").find(":not(tbody *)").unbind(".DT"),i(t).unbind(".DT-"+n.sInstance),c!=u.parentNode&&(f.children("thead").detach(),f.append(u)),d&&c!=d.parentNode&&(f.children("tfoot").detach(),f.append(d)),f.detach(),g.detach(),n.aaSorting=[],n.aaSortingFixed=[],se(n),i(m).removeClass(n.asStripeClasses.join(" ")),i("th, td",u).removeClass(l.sSortable+" "+l.sSortableAsc+" "+l.sSortableDesc+" "+l.sSortableNone),n.bJUI&&(i("th span."+l.sSortIcon+", td span."+l.sSortIcon,u).detach(),i("th, td",u).each(function(){var t=i("div."+l.sSortJUIWrapper,this);i(this).append(t.contents()),t.detach()})),!e&&a&&a.insertBefore(c,n.nTableReinsertBefore),p.children().detach(),p.append(m),f.css("width",n.sDestroyWidth).removeClass(l.sTable),(r=n.asDestroyStripes.length)&&p.children().each(function(t){i(this).addClass(n.asDestroyStripes[t%r])});var v=i.inArray(n,s.settings);-1!==v&&s.settings.splice(v,1)})}),s.version="1.10.4",s.settings=[],s.models={},s.models.oSearch={bCaseInsensitive:!0,sSearch:"",bRegex:!1,bSmart:!0},s.models.oRow={nTr:null,anCells:null,_aData:[],_aSortData:null,_aFilterData:null,_sFilterRow:null,_sRowStripe:"",src:null},s.models.oColumn={idx:null,aDataSort:null,asSorting:null,bSearchable:null,bSortable:null,bVisible:null,_sManualType:null,_bAttrSrc:!1,fnCreatedCell:null,fnGetData:null,fnSetData:null,mData:null,mRender:null,nTh:null,nTf:null,sClass:null,sContentPadding:null,sDefaultContent:null,sName:null,sSortDataType:"std",sSortingClass:null,sSortingClassJUI:null,sTitle:null,sType:null,sWidth:null,sWidthOrig:null},s.defaults={aaData:null,aaSorting:[[0,"asc"]],aaSortingFixed:[],ajax:null,aLengthMenu:[10,25,50,100],aoColumns:null,aoColumnDefs:null,aoSearchCols:[],asStripeClasses:null,bAutoWidth:!0,bDeferRender:!1,bDestroy:!1,bFilter:!0,bInfo:!0,bJQueryUI:!1,bLengthChange:!0,bPaginate:!0,bProcessing:!1,bRetrieve:!1,bScrollCollapse:!1,bServerSide:!1,bSort:!0,bSortMulti:!0,bSortCellsTop:!1,bSortClasses:!0,bStateSave:!1,fnCreatedRow:null,fnDrawCallback:null,fnFooterCallback:null,fnFormatNumber:function(t){return t.toString().replace(/\B(?=(\d{3})+(?!\d))/g,this.oLanguage.sThousands)},fnHeaderCallback:null,fnInfoCallback:null,fnInitComplete:null,fnPreDrawCallback:null,fnRowCallback:null,fnServerData:null,fnServerParams:null,fnStateLoadCallback:function(t){try{return JSON.parse((-1===t.iStateDuration?sessionStorage:localStorage).getItem("DataTables_"+t.sInstance+"_"+location.pathname))}catch(t){}},fnStateLoadParams:null,fnStateLoaded:null,fnStateSaveCallback:function(t,e){try{(-1===t.iStateDuration?sessionStorage:localStorage).setItem("DataTables_"+t.sInstance+"_"+location.pathname,JSON.stringify(e))}catch(t){}},fnStateSaveParams:null,iStateDuration:7200,iDeferLoading:null,iDisplayLength:10,iDisplayStart:0,iTabIndex:0,oClasses:{},oLanguage:{oAria:{sSortAscending:": activate to sort column ascending",sSortDescending:": activate to sort column descending"},oPaginate:{sFirst:"First",sLast:"Last",sNext:"Next",sPrevious:"Previous"},sEmptyTable:"No data available in table",sInfo:"Showing _START_ to _END_ of _TOTAL_ entries",sInfoEmpty:"Showing 0 to 0 of 0 entries",sInfoFiltered:"(filtered from _MAX_ total entries)",sInfoPostFix:"",sDecimal:"",sThousands:",",sLengthMenu:"Show _MENU_ entries",sLoadingRecords:"Loading...",sProcessing:"Processing...",sSearch:"Search:",sSearchPlaceholder:"",sUrl:"",sZeroRecords:"No matching records found"},oSearch:i.extend({},s.models.oSearch),sAjaxDataProp:"data",sAjaxSource:null,sDom:"lfrtip",searchDelay:null,sPaginationType:"simple_numbers",sScrollX:"",sScrollXInner:"",sScrollY:"",sServerMethod:"GET",renderer:null},E(s.defaults),s.defaults.column={aDataSort:null,iDataSort:-1,asSorting:["asc","desc"],bSearchable:!0,bSortable:!0,bVisible:!0,fnCreatedCell:null,mData:null,mRender:null,sCellType:"td",sClass:"",sContentPadding:"",sDefaultContent:null,sName:"",sSortDataType:"std",sTitle:null,sType:null,sWidth:null},E(s.defaults.column),s.models.oSettings={oFeatures:{bAutoWidth:null,bDeferRender:null,bFilter:null,bInfo:null,bLengthChange:null,bPaginate:null,bProcessing:null,bServerSide:null,bSort:null,bSortMulti:null,bSortClasses:null,bStateSave:null},oScroll:{bCollapse:null,iBarWidth:0,sX:null,sXInner:null,sY:null},oLanguage:{fnInfoCallback:null},oBrowser:{bScrollOversize:!1,bScrollbarLeft:!1},ajax:null,aanFeatures:[],aoData:[],aiDisplay:[],aiDisplayMaster:[],aoColumns:[],aoHeader:[],aoFooter:[],oPreviousSearch:{},aoPreSearchCols:[],aaSorting:null,aaSortingFixed:[],asStripeClasses:null,asDestroyStripes:[],sDestroyWidth:0,aoRowCallback:[],aoHeaderCallback:[],aoFooterCallback:[],aoDrawCallback:[],aoRowCreatedCallback:[],aoPreDrawCallback:[],aoInitComplete:[],aoStateSaveParams:[],aoStateLoadParams:[],aoStateLoaded:[],sTableId:"",nTable:null,nTHead:null,nTFoot:null,nTBody:null,nTableWrapper:null,bDeferLoading:!1,bInitialised:!1,aoOpenRows:[],sDom:null,searchDelay:null,sPaginationType:"two_button",iStateDuration:0,aoStateSave:[],aoStateLoad:[],oSavedState:null,oLoadedState:null,sAjaxSource:null,sAjaxDataProp:null,bAjaxDataGet:!0,jqXHR:null,json:n,oAjaxData:n,fnServerData:null,aoServerParams:[],sServerMethod:null,fnFormatNumber:null,aLengthMenu:null,iDraw:0,bDrawing:!1,iDrawError:-1,_iDisplayLength:10,_iDisplayStart:0,_iRecordsTotal:0,_iRecordsDisplay:0,bJUI:null,oClasses:{},bFiltered:!1,bSorted:!1,bSortCellsTop:null,oInit:null,aoDestroyCallback:[],fnRecordsTotal:function(){return"ssp"==ve(this)?1*this._iRecordsTotal:this.aiDisplayMaster.length},fnRecordsDisplay:function(){return"ssp"==ve(this)?1*this._iRecordsDisplay:this.aiDisplay.length},fnDisplayEnd:function(){var t=this._iDisplayLength,e=this._iDisplayStart,n=e+t,i=this.aiDisplay.length,s=this.oFeatures,r=s.bPaginate;return s.bServerSide?!1===r||-1===t?e+i:Math.min(e+t,this._iRecordsDisplay):!r||n>i||-1===t?i:n},oInstance:null,sInstance:null,iTabIndex:0,nScrollHead:null,nScrollFoot:null,aLastSort:[],oPlugins:{}},s.ext=r={classes:{},errMode:"alert",feature:[],search:[],internal:{},legacy:{ajax:null},pager:{},renderer:{pageButton:{},header:{}},order:{},type:{detect:[],search:{},order:{}},_unique:0,fnVersionCheck:s.fnVersionCheck,iApiIndex:0,oJUIClasses:{},sVersion:s.version},i.extend(r,{afnFiltering:r.search,aTypes:r.type.detect,ofnSearch:r.type.search,oSort:r.type.order,afnSortData:r.order,aoFeatures:r.feature,oApi:r.internal,oStdClasses:r.classes,oPagination:r.pager}),i.extend(s.ext.classes,{sTable:"dataTable",sNoFooter:"no-footer",sPageButton:"paginate_button",sPageButtonActive:"current",sPageButtonDisabled:"disabled",sStripeOdd:"odd",sStripeEven:"even",sRowEmpty:"dataTables_empty",sWrapper:"dataTables_wrapper",sFilter:"dataTables_filter",sInfo:"dataTables_info",sPaging:"dataTables_paginate paging_",sLength:"dataTables_length",sProcessing:"dataTables_processing",sSortAsc:"sorting_asc",sSortDesc:"sorting_desc",sSortable:"sorting",sSortableAsc:"sorting_asc_disabled",sSortableDesc:"sorting_desc_disabled",sSortableNone:"sorting_disabled",sSortColumn:"sorting_",sFilterInput:"",sLengthSelect:"",sScrollWrapper:"dataTables_scroll",sScrollHead:"dataTables_scrollHead",sScrollHeadInner:"dataTables_scrollHeadInner",sScrollBody:"dataTables_scrollBody",sScrollFoot:"dataTables_scrollFoot",sScrollFootInner:"dataTables_scrollFootInner",sHeaderTH:"",sFooterTH:"",sSortJUIAsc:"",sSortJUIDesc:"",sSortJUI:"",sSortJUIAscAllowed:"",sSortJUIDescAllowed:"",sSortJUIWrapper:"",sSortIcon:"",sJUIHeader:"",sJUIFooter:""}),function(){var t="ui-state-default",e="css_right ui-icon ui-icon-",n="fg-toolbar ui-toolbar ui-widget-header ui-helper-clearfix";i.extend(s.ext.oJUIClasses,s.ext.classes,{sPageButton:"fg-button ui-button "+t,sPageButtonActive:"ui-state-disabled",sPageButtonDisabled:"ui-state-disabled",sPaging:"dataTables_paginate fg-buttonset ui-buttonset fg-buttonset-multi ui-buttonset-multi paging_",sSortAsc:t+" sorting_asc",sSortDesc:t+" sorting_desc",sSortable:t+" sorting",sSortableAsc:t+" sorting_asc_disabled",sSortableDesc:t+" sorting_desc_disabled",sSortableNone:t+" sorting_disabled",sSortJUIAsc:e+"triangle-1-n",sSortJUIDesc:e+"triangle-1-s",sSortJUI:e+"carat-2-n-s",sSortJUIAscAllowed:e+"carat-1-n",sSortJUIDescAllowed:e+"carat-1-s",sSortJUIWrapper:"DataTables_sort_wrapper",sSortIcon:"DataTables_sort_icon",sScrollHead:"dataTables_scrollHead "+t,sScrollFoot:"dataTables_scrollFoot "+t,sHeaderTH:t,sFooterTH:t,sJUIHeader:n+" ui-corner-tl ui-corner-tr",sJUIFooter:n+" ui-corner-bl ui-corner-br"})}();var ke=s.ext.pager;function Oe(t,e){var n=[],i=ke.numbers_length,s=Math.floor(i/2);return e<=i?n=S(0,e):t<=s?((n=S(0,i-2)).push("ellipsis"),n.push(e-1)):t>=e-1-s?((n=S(e-(i-2),e)).splice(0,0,"ellipsis"),n.splice(0,0,0)):((n=S(t-1,t+2)).push("ellipsis"),n.push(e-1),n.splice(0,0,"ellipsis"),n.splice(0,0,0)),n.DT_el="span",n}i.extend(ke,{simple:function(t,e){return["previous","next"]},full:function(t,e){return["first","previous","next","last"]},simple_numbers:function(t,e){return["previous",Oe(t,e),"next"]},full_numbers:function(t,e){return["first","previous",Oe(t,e),"next","last"]},_numbers:Oe,numbers_length:7}),i.extend(!0,s.ext.renderer,{pageButton:{_:function(t,n,s,r,o,a){var l,c,h=t.oClasses,u=t.oLanguage.oPaginate,d=0,f=function(e,n){var r,p,g,m=function(e){Mt(t,e.data.action,!0)};for(r=0,p=n.length;r<p;r++)if(g=n[r],i.isArray(g)){var v=i("<"+(g.DT_el||"div")+"/>").appendTo(e);f(v,g)}else{switch(l="",c="",g){case"ellipsis":e.append("<span>&hellip;</span>");break;case"first":l=u.sFirst,c=g+(o>0?"":" "+h.sPageButtonDisabled);break;case"previous":l=u.sPrevious,c=g+(o>0?"":" "+h.sPageButtonDisabled);break;case"next":l=u.sNext,c=g+(o<a-1?"":" "+h.sPageButtonDisabled);break;case"last":l=u.sLast,c=g+(o<a-1?"":" "+h.sPageButtonDisabled);break;default:l=g+1,c=o===g?h.sPageButtonActive:""}l&&(de(i("<a>",{class:h.sPageButton+" "+c,"aria-controls":t.sTableId,"data-dt-idx":d,tabindex:t.iTabIndex,id:0===s&&"string"==typeof g?t.sTableId+"_"+g:null}).html(l).appendTo(e),{action:g},m),d++)}};try{var p=i(e.activeElement).data("dt-idx");f(i(n).empty(),r),null!==p&&i(n).find("[data-dt-idx="+p+"]").focus()}catch(t){}}}}),i.extend(s.ext.type.detect,[function(t,e){var n=e.oLanguage.sDecimal;return y(t,n)?"num"+n:null},function(t,e){if(t&&!(t instanceof Date)&&(!d.test(t)||!f.test(t)))return null;var n=Date.parse(t);return null!==n&&!isNaN(n)||m(t)?"date":null},function(t,e){var n=e.oLanguage.sDecimal;return y(t,n,!0)?"num-fmt"+n:null},function(t,e){var n=e.oLanguage.sDecimal;return _(t,n)?"html-num"+n:null},function(t,e){var n=e.oLanguage.sDecimal;return _(t,n,!0)?"html-num-fmt"+n:null},function(t,e){return m(t)||"string"==typeof t&&-1!==t.indexOf("<")?"html":null}]),i.extend(s.ext.type.search,{html:function(t){return m(t)?t:"string"==typeof t?t.replace(h," ").replace(u,""):""},string:function(t){return m(t)?t:"string"==typeof t?t.replace(h," "):t}});var Ne=function(t,e,n,i){return 0===t||t&&"-"!==t?(e&&(t=b(t,e)),t.replace&&(n&&(t=t.replace(n,"")),i&&(t=t.replace(i,""))),1*t):-1/0};function Pe(t){i.each({num:function(e){return Ne(e,t)},"num-fmt":function(e){return Ne(e,t,g)},"html-num":function(e){return Ne(e,t,u)},"html-num-fmt":function(e){return Ne(e,t,u,g)}},function(e,n){r.type.order[e+t+"-pre"]=n,e.match(/^html\-/)&&(r.type.search[e+t]=r.type.search.html)})}function Le(t){return function(){var e=[le(this[s.ext.iApiIndex])].concat(Array.prototype.slice.call(arguments));return s.ext.internal[t].apply(this,e)}}return i.extend(r.type.order,{"date-pre":function(t){return Date.parse(t)||0},"html-pre":function(t){return m(t)?"":t.replace?t.replace(/<.*?>/g,"").toLowerCase():t+""},"string-pre":function(t){return m(t)?"":"string"==typeof t?t.toLowerCase():t.toString?t.toString():""},"string-asc":function(t,e){return t<e?-1:t>e?1:0},"string-desc":function(t,e){return t<e?1:t>e?-1:0}}),Pe(""),i.extend(!0,s.ext.renderer,{header:{_:function(t,e,n,s){i(t.nTable).on("order.dt.DT",function(i,r,o,a){if(t===r){var l=n.idx;e.removeClass(n.sSortingClass+" "+s.sSortAsc+" "+s.sSortDesc).addClass("asc"==a[l]?s.sSortAsc:"desc"==a[l]?s.sSortDesc:n.sSortingClass)}})},jqueryui:function(t,e,n,s){i("<div/>").addClass(s.sSortJUIWrapper).append(e.contents()).append(i("<span/>").addClass(s.sSortIcon+" "+n.sSortingClassJUI)).appendTo(e),i(t.nTable).on("order.dt.DT",function(i,r,o,a){if(t===r){var l=n.idx;e.removeClass(s.sSortAsc+" "+s.sSortDesc).addClass("asc"==a[l]?s.sSortAsc:"desc"==a[l]?s.sSortDesc:n.sSortingClass),e.find("span."+s.sSortIcon).removeClass(s.sSortJUIAsc+" "+s.sSortJUIDesc+" "+s.sSortJUI+" "+s.sSortJUIAscAllowed+" "+s.sSortJUIDescAllowed).addClass("asc"==a[l]?s.sSortJUIAsc:"desc"==a[l]?s.sSortJUIDesc:n.sSortingClassJUI)}})}}}),s.render={number:function(t,e,n,i){return{display:function(s){var r=s<0?"-":"";s=Math.abs(parseFloat(s));var o=parseInt(s,10),a=n?e+(s-o).toFixed(n).substring(2):"";return r+(i||"")+o.toString().replace(/\B(?=(\d{3})+(?!\d))/g,t)+a}}}},i.extend(s.ext.internal,{_fnExternApiFunc:Le,_fnBuildAjax:ft,_fnAjaxUpdate:pt,_fnAjaxParameters:gt,_fnAjaxUpdateDraw:mt,_fnAjaxDataSrc:vt,_fnAddColumn:R,_fnColumnOptions:F,_fnAdjustColumnSizing:j,_fnVisibleToColumnIndex:M,_fnColumnIndexToVisible:H,_fnVisbleColumns:$,_fnGetColumns:W,_fnColumnTypes:U,_fnApplyColumnDefs:V,_fnHungarianMap:E,_fnCamelToHungarian:I,_fnLanguageCompat:A,_fnBrowserDetect:P,_fnAddData:B,_fnAddTr:z,_fnNodeToDataIndex:function(t,e){return e._DT_RowIndex!==n?e._DT_RowIndex:null},_fnNodeToColumnIndex:function(t,e,n){return i.inArray(n,t.aoData[e].anCells)},_fnGetCellData:K,_fnSetCellData:q,_fnSplitObjNotation:Y,_fnGetObjectDataFn:J,_fnSetObjectDataFn:X,_fnGetDataMaster:Z,_fnClearTable:tt,_fnDeleteIndex:et,_fnInvalidate:nt,_fnGetRowElements:it,_fnCreateTr:st,_fnBuildHead:ot,_fnDrawHead:at,_fnDraw:lt,_fnReDraw:ct,_fnAddOptionsHtml:ht,_fnDetectHeader:ut,_fnGetUniqueThs:dt,_fnFeatureHtmlFilter:bt,_fnFilterComplete:yt,_fnFilterCustom:_t,_fnFilterColumn:wt,_fnFilter:Ct,_fnFilterCreateSearch:St,_fnEscapeRegex:xt,_fnFilterData:Et,_fnFeatureHtmlInfo:kt,_fnUpdateInfo:Ot,_fnInfoMacros:Nt,_fnInitialise:Pt,_fnInitComplete:Lt,_fnLengthChange:Rt,_fnFeatureHtmlLength:Ft,_fnFeatureHtmlPaginate:jt,_fnPageChange:Mt,_fnFeatureHtmlProcessing:Ht,_fnProcessingDisplay:$t,_fnFeatureHtmlTable:Wt,_fnScrollDraw:Ut,_fnApplyToChildren:Vt,_fnCalculateColumnWidths:zt,_fnThrottle:Kt,_fnConvertToWidth:qt,_fnScrollingWidthAdjust:Gt,_fnGetWidestNode:Qt,_fnGetMaxLenString:Yt,_fnStringToCss:Jt,_fnScrollBarWidth:Xt,_fnSortFlatten:Zt,_fnSort:te,_fnSortAria:ee,_fnSortListener:ne,_fnSortAttachListener:ie,_fnSortingClasses:se,_fnSortData:re,_fnSaveState:oe,_fnLoadState:ae,_fnSettingsFromNode:le,_fnLog:ce,_fnMap:he,_fnBindAction:de,_fnCallbackReg:fe,_fnCallbackFire:pe,_fnLengthOverflow:ge,_fnRenderer:me,_fnDataSource:ve,_fnRowAttributes:rt,_fnCalculateEnd:function(){}}),i.fn.dataTable=s,i.fn.dataTableSettings=s.settings,i.fn.dataTableExt=s.ext,i.fn.DataTable=function(t){return i(this).dataTable(t).api()},i.each(s,function(t,e){i.fn.DataTable[t]=e}),i.fn.dataTable})}(window,document),function(t,e){"use strict";var n=function(t,e){this.init("radiocheck",t,e)};n.DEFAULTS={checkboxClass:"custom-checkbox",radioClass:"custom-radio",checkboxTemplate:'<span class="icons"><span class="icon-unchecked"></span><span class="icon-checked"></span></span>',radioTemplate:'<span class="icons"><span class="icon-unchecked"></span><span class="icon-checked"></span></span>'},n.prototype.init=function(t,i,s){this.$element=e(i),this.options=e.extend({},n.DEFAULTS,this.$element.data(),s),"checkbox"==this.$element.attr("type")?(this.$element.addClass(this.options.checkboxClass),this.$element.after(this.options.checkboxTemplate)):"radio"==this.$element.attr("type")&&(this.$element.addClass(this.options.radioClass),this.$element.after(this.options.radioTemplate))},n.prototype.check=function(){this.$element.prop("checked",!0),this.$element.trigger("change.radiocheck").trigger("checked.radiocheck")},n.prototype.uncheck=function(){this.$element.prop("checked",!1),this.$element.trigger("change.radiocheck").trigger("unchecked.radiocheck")},n.prototype.toggle=function(){this.$element.prop("checked",function(t,e){return!e}),this.$element.trigger("change.radiocheck").trigger("toggled.radiocheck")},n.prototype.indeterminate=function(){this.$element.prop("indeterminate",!0),this.$element.trigger("change.radiocheck").trigger("indeterminated.radiocheck")},n.prototype.determinate=function(){this.$element.prop("indeterminate",!1),this.$element.trigger("change.radiocheck").trigger("determinated.radiocheck")},n.prototype.disable=function(){this.$element.prop("disabled",!0),this.$element.trigger("change.radiocheck").trigger("disabled.radiocheck")},n.prototype.enable=function(){this.$element.prop("disabled",!1),this.$element.trigger("change.radiocheck").trigger("enabled.radiocheck")},n.prototype.destroy=function(){this.$element.removeData().removeClass(this.options.checkboxClass+" "+this.options.radioClass).next(".icons").remove(),this.$element.trigger("destroyed.radiocheck")};var i=e.fn.radiocheck;e.fn.radiocheck=function(i){return this.each(function(){var s=e(this),r=s.data("radiocheck"),o="object"==typeof i&&i;(r||"destroy"!=i)&&(r||s.data("radiocheck",r=new n(this,o)),"string"==typeof i&&r[i](),!0===/mobile|tablet|phone|ip(ad|od)|android|silk|webos/i.test(t.navigator.userAgent)&&s.parent().hover(function(){s.addClass("nohover")},function(){s.removeClass("nohover")}))})},e.fn.radiocheck.Constructor=n,e.fn.radiocheck.noConflict=function(){return e.fn.radiocheck=i,this}}(this,jQuery);